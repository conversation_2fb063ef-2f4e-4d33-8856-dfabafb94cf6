<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern POS System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .pos-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            padding: 32px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 900px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            margin-bottom: 32px;
        }

        .transaction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .transaction-id {
            font-size: 14px;
            color: #666;
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            font-family: 'Courier New', monospace;
        }

        .payment-summary {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .payment-stat {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 16px;
            min-width: 120px;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }

        .payment-stat.total {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            border: none;
        }

        .payment-stat.paid {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
        }

        .payment-stat.remaining {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }

        .payment-methods-section {
            margin-bottom: 20px;
        }

        .payment-methods-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .payment-methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
        }

        .payment-method-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            min-height: 60px;
        }

        .payment-method-card:hover {
            border-color: #667eea;
            background: #fff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .payment-method-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .payment-icon-small {
            width: 36px;
            height: 36px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: #667eea;
            color: white;
            flex-shrink: 0;
        }

        .payment-method-card.selected .payment-icon-small {
            background: rgba(255, 255, 255, 0.2);
        }

        .payment-method-info {
            flex: 1;
            min-width: 0;
        }

        .payment-method-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .payment-method-desc {
            font-size: 12px;
            opacity: 0.7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 32px;
            border: 1px solid #e9ecef;
        }

        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .order-item:last-child {
            border-bottom: none;
        }

        .item-details {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .item-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #ff6b6b, #ffa726);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
        }

        .item-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .item-info p {
            font-size: 14px;
            color: #666;
        }

        .item-price {
            font-size: 18px;
            font-weight: 700;
            color: #333;
        }

        .total-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 32px;
            text-align: center;
        }

        .total-amount {
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 8px;
        }

        .total-label {
            font-size: 16px;
            opacity: 0.9;
        }

        .keypad-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 32px;
            margin-bottom: 32px;
        }

        .keypad {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .key {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            padding: 20px;
            font-size: 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 80px;
        }

        .key:hover {
            background: #e9ecef;
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .key:active {
            transform: translateY(0);
        }

        .key.clear {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            border: none;
        }

        .key.clear:hover {
            background: linear-gradient(135deg, #ee5a52, #ff6b6b);
        }

        .display-screen {
            background: #1a1a1a;
            color: #00ff88;
            border-radius: 16px;
            padding: 24px;
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            text-align: right;
            border: 2px solid #333;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .payment-method {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 16px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .payment-method:hover {
            border-color: #667eea;
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .payment-method.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .payment-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .payment-method.selected .payment-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        .payment-method h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .payment-method p {
            font-size: 14px;
            opacity: 0.7;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            margin-top: 32px;
        }

        .btn {
            flex: 1;
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        @media (max-width: 768px) {
            .keypad-section {
                grid-template-columns: 1fr;
            }
            
            .payment-methods {
                grid-template-columns: 1fr;
            }
            
            .transaction-info {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }
            
            .payment-stats {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="pos-container">
        <div class="header">
            <div class="transaction-header">
                <div class="transaction-id">CHK_ZEOXZUX8</div>
                <div class="payment-summary">
                    <div class="payment-stat total">Total: GH₵23</div>
                    <div class="payment-stat paid">Paid: GH₵10</div>
                    <div class="payment-stat remaining">Remaining: GH₵13</div>
                </div>
            </div>
        </div>

        <div class="order-summary">
            <div class="order-item">
                <div class="item-details">
                    <div class="item-icon">🥤</div>
                    <div class="item-info">
                        <h3>Fanta Orange 500ml</h3>
                        <p>Quantity: 1</p>
                    </div>
                </div>
                <div class="item-price">GH₵23.00</div>
            </div>
        </div>

        <div class="total-section">
            <div class="total-amount">GH₵23.00</div>
            <div class="total-label">Total Amount</div>
        </div>

        <div class="keypad-section">
            <div class="keypad">
                <div class="key" onclick="addDigit('1')">1</div>
                <div class="key" onclick="addDigit('2')">2</div>
                <div class="key" onclick="addDigit('3')">3</div>
                <div class="key" onclick="addDigit('4')">4</div>
                <div class="key" onclick="addDigit('5')">5</div>
                <div class="key" onclick="addDigit('6')">6</div>
                <div class="key" onclick="addDigit('7')">7</div>
                <div class="key" onclick="addDigit('8')">8</div>
                <div class="key" onclick="addDigit('9')">9</div>
                <div class="key" onclick="addDigit('0')">0</div>
                <div class="key" onclick="addDigit('.')">.</div>
                <div class="key clear" onclick="clearDisplay()">C</div>
            </div>
            <div class="display-screen" id="display">0.00</div>
        </div>

        <div class="payment-methods-section">
            <div class="payment-methods-title">Payment Methods</div>
            <div class="payment-methods-grid">
                <div class="payment-method-card" onclick="selectPaymentMethod('cash')">
                    <div class="payment-icon-small">💵</div>
                    <div class="payment-method-info">
                        <div class="payment-method-name">Cash</div>
                        <div class="payment-method-desc">Pay with cash</div>
                    </div>
                </div>
                <div class="payment-method-card" onclick="selectPaymentMethod('mastercard')">
                    <div class="payment-icon-small">💳</div>
                    <div class="payment-method-info">
                        <div class="payment-method-name">Master Card</div>
                        <div class="payment-method-desc">****1234</div>
                    </div>
                </div>
                <div class="payment-method-card" onclick="selectPaymentMethod('visa')">
                    <div class="payment-icon-small">💳</div>
                    <div class="payment-method-info">
                        <div class="payment-method-name">Visa Card</div>
                        <div class="payment-method-desc">****5678</div>
                    </div>
                </div>
                <div class="payment-method-card" onclick="selectPaymentMethod('mobile')">
                    <div class="payment-icon-small">📱</div>
                    <div class="payment-method-info">
                        <div class="payment-method-name">Mobile Money</div>
                        <div class="payment-method-desc">MTN/Vodafone/AirtelTigo</div>
                    </div>
                </div>
                <div class="payment-method-card" onclick="selectPaymentMethod('pay0')">
                    <div class="payment-icon-small">💰</div>
                    <div class="payment-method-info">
                        <div class="payment-method-name">PAY 0</div>
                        <div class="payment-method-desc">Zero payment option</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-secondary" onclick="cancelTransaction()">Cancel</button>
            <button class="btn btn-primary" onclick="processPayment()">Process Payment</button>
        </div>
    </div>

    <script>
        let currentAmount = '';
        let selectedPayment = '';

        function addDigit(digit) {
            if (currentAmount === '' && digit === '.') {
                currentAmount = '0.';
            } else if (currentAmount === '' && digit !== '.') {
                currentAmount = digit;
            } else if (digit === '.' && currentAmount.includes('.')) {
                return;
            } else {
                currentAmount += digit;
            }
            updateDisplay();
        }

        function clearDisplay() {
            currentAmount = '';
            updateDisplay();
        }

        function updateDisplay() {
            const display = document.getElementById('display');
            display.textContent = currentAmount || '0.00';
        }

        function selectPaymentMethod(method) {
            // Remove previous selection
            document.querySelectorAll('.payment-method-card').forEach(el => {
                el.classList.remove('selected');
            });
            
            // Add selection to clicked method
            event.target.closest('.payment-method-card').classList.add('selected');
            selectedPayment = method;
        }

        function processPayment() {
            if (!selectedPayment) {
                alert('Please select a payment method');
                return;
            }
            
            if (!currentAmount || parseFloat(currentAmount) === 0) {
                alert('Please enter an amount');
                return;
            }
            
            alert(`Processing ${selectedPayment} payment of GH₵${currentAmount}`);
        }

        function cancelTransaction() {
            if (confirm('Are you sure you want to cancel this transaction?')) {
                currentAmount = '';
                selectedPayment = '';
                document.querySelectorAll('.payment-method').forEach(el => {
                    el.classList.remove('selected');
                });
                updateDisplay();
            }
        }
    </script>
</body>
</html>