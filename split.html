<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bill Split - POS System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #1a202c;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 30px 40px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.1;
        }

        .header-content {
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 28px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .split-icon {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            border-radius: 12px;
            padding: 10px;
            font-size: 24px;
        }

        .table-info {
            background: rgba(255,255,255,0.15);
            padding: 15px 25px;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }

        /* Original Bill Section */
        .original-bill {
            padding: 40px;
            border-right: 1px solid #e2e8f0;
            background: #fafafa;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .order-status {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #718096, #4a5568);
            color: white;
            box-shadow: 0 4px 15px rgba(113, 128, 150, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            box-shadow: 0 4px 15px rgba(72, 187, 120, 0.4);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .btn:disabled {
            background: #e2e8f0;
            color: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Menu Items */
        .menu-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .menu-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .menu-item.selected {
            border-color: #fd79a8;
            box-shadow: 0 8px 25px rgba(253, 121, 168, 0.3);
        }

        .menu-item.selected::before {
            opacity: 0.05;
        }

        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .item-content {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .item-checkbox {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            border: 2px solid #cbd5e0;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .item-checkbox.checked {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            border-color: #fd79a8;
        }

        .item-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .item-details {
            flex: 1;
        }

        .item-name {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .item-price {
            font-size: 14px;
            color: #718096;
        }

        .item-quantity {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .quantity-controls {
            display: none;
            align-items: center;
            gap: 12px;
            margin-left: 20px;
            background: #f7fafc;
            padding: 8px 16px;
            border-radius: 25px;
        }

        .quantity-controls.show {
            display: flex;
        }

        .quantity-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quantity-btn:hover {
            transform: scale(1.1);
        }

        .quantity-btn:disabled {
            background: #e2e8f0;
            color: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .split-quantity {
            width: 50px;
            text-align: center;
            border: 2px solid #fd79a8;
            border-radius: 8px;
            padding: 4px;
            font-weight: 600;
            color: #fd79a8;
        }

        .remaining-qty {
            font-size: 12px;
            color: #718096;
            background: #e2e8f0;
            padding: 4px 12px;
            border-radius: 12px;
        }

        .item-total {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            min-width: 80px;
            text-align: right;
        }

        /* Split Preview Section */
        .split-preview {
            padding: 40px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        }

        .new-table-card {
            background: linear-gradient(135deg, #fd79a8, #e84393);
            color: white;
            padding: 25px;
            border-radius: 16px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .new-table-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .new-table-card h3 {
            font-size: 20px;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .new-table-card p {
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .split-items {
            margin-bottom: 30px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .split-item {
            background: white;
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-left: 4px solid #fd79a8;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .split-item-info {
            flex: 1;
        }

        .split-item-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .split-item-details {
            font-size: 14px;
            color: #718096;
        }

        .split-item-total {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
        }

        /* Totals */
        .totals-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .total-row:last-child {
            border-bottom: none;
            margin-top: 12px;
            padding-top: 20px;
            border-top: 2px solid #e2e8f0;
        }

        .total-row.grand {
            font-size: 20px;
            font-weight: 700;
            color: #2d3748;
        }

        .total-label {
            color: #718096;
            font-weight: 500;
        }

        .total-amount {
            font-weight: 600;
            color: #2d3748;
        }

        /* Success Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 1000;
            backdrop-filter: blur(8px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 24px;
            padding: 40px;
            width: 500px;
            max-width: 90vw;
            box-shadow: 0 25px 50px rgba(0,0,0,0.3);
            text-align: center;
            animation: modalSlideIn 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #fd79a8, #e84393, #667eea, #764ba2);
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #48bb78, #38a169);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            margin: 0 auto 25px;
            animation: successPulse 2s infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 16px;
        }

        .modal-details {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .modal-details strong {
            color: #2d3748;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .original-bill {
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .original-bill,
            .split-preview {
                padding: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="header-title">
                    <div class="split-icon">✂️</div>
                    Split Bill
                </div>
                <div class="table-info">
                    🍽️ Table 5 - Order #1234
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Original Bill Section -->
            <div class="original-bill">
                <div class="section-header">
                    <div class="section-title">
                        <span>📋</span>
                        Original Bill
                    </div>
                    <div class="order-status">Prepared</div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="selectAllItems()" id="selectAllBtn">
                        <span>☑️</span> Select All
                    </button>
                    <button class="btn btn-secondary" onclick="clearSelection()">
                        <span>❌</span> Clear
                    </button>
                    <button class="btn btn-success" onclick="confirmSplit()" id="confirmBtn" disabled>
                        <span>✂️</span> Split Bill
                    </button>
                </div>

                <div class="menu-items">
                    <div class="menu-item" onclick="toggleItem(this, 'item1')">
                        <div class="item-content">
                            <div class="item-checkbox" id="checkbox1"></div>
                            <div class="item-details">
                                <div class="item-name">Grilled Chicken Breast</div>
                                <div class="item-price">$18.50 each</div>
                            </div>
                            <div class="item-quantity">4</div>
                            <div class="quantity-controls" id="controls1">
                                <button class="quantity-btn" onclick="decreaseQuantity(event, 'item1')">−</button>
                                <input type="number" class="split-quantity" value="1" min="1" max="3" id="qty1">
                                <button class="quantity-btn" onclick="increaseQuantity(event, 'item1')">+</button>
                                <div class="remaining-qty" id="remaining1">Remaining: 3</div>
                            </div>
                            <div class="item-total">$74.00</div>
                        </div>
                    </div>

                    <div class="menu-item" onclick="toggleItem(this, 'item2')">
                        <div class="item-content">
                            <div class="item-checkbox" id="checkbox2"></div>
                            <div class="item-details">
                                <div class="item-name">Caesar Salad</div>
                                <div class="item-price">$12.00 each</div>
                            </div>
                            <div class="item-quantity">2</div>
                            <div class="quantity-controls" id="controls2">
                                <button class="quantity-btn" onclick="decreaseQuantity(event, 'item2')">−</button>
                                <input type="number" class="split-quantity" value="1" min="1" max="1" id="qty2">
                                <button class="quantity-btn" onclick="increaseQuantity(event, 'item2')">+</button>
                                <div class="remaining-qty" id="remaining2">Remaining: 1</div>
                            </div>
                            <div class="item-total">$24.00</div>
                        </div>
                    </div>

                    <div class="menu-item" onclick="toggleItem(this, 'item3')">
                        <div class="item-content">
                            <div class="item-checkbox" id="checkbox3"></div>
                            <div class="item-details">
                                <div class="item-name">Pasta Carbonara</div>
                                <div class="item-price">$16.50 each</div>
                            </div>
                            <div class="item-quantity">3</div>
                            <div class="quantity-controls" id="controls3">
                                <button class="quantity-btn" onclick="decreaseQuantity(event, 'item3')">−</button>
                                <input type="number" class="split-quantity" value="1" min="1" max="2" id="qty3">
                                <button class="quantity-btn" onclick="increaseQuantity(event, 'item3')">+</button>
                                <div class="remaining-qty" id="remaining3">Remaining: 2</div>
                            </div>
                            <div class="item-total">$49.50</div>
                        </div>
                    </div>

                    <div class="menu-item" onclick="toggleItem(this, 'item4')">
                        <div class="item-content">
                            <div class="item-checkbox" id="checkbox4"></div>
                            <div class="item-details">
                                <div class="item-name">House Wine</div>
                                <div class="item-price">$8.00 each</div>
                            </div>
                            <div class="item-quantity">6</div>
                            <div class="quantity-controls" id="controls4">
                                <button class="quantity-btn" onclick="decreaseQuantity(event, 'item4')">−</button>
                                <input type="number" class="split-quantity" value="1" min="1" max="5" id="qty4">
                                <button class="quantity-btn" onclick="increaseQuantity(event, 'item4')">+</button>
                                <div class="remaining-qty" id="remaining4">Remaining: 5</div>
                            </div>
                            <div class="item-total">$48.00</div>
                        </div>
                    </div>

                    <div class="menu-item" onclick="toggleItem(this, 'item5')">
                        <div class="item-content">
                            <div class="item-checkbox" id="checkbox5"></div>
                            <div class="item-details">
                                <div class="item-name">Chocolate Dessert</div>
                                <div class="item-price">$9.50 each</div>
                            </div>
                            <div class="item-quantity">2</div>
                            <div class="quantity-controls" id="controls5">
                                <button class="quantity-btn" onclick="decreaseQuantity(event, 'item5')">−</button>
                                <input type="number" class="split-quantity" value="1" min="1" max="1" id="qty5">
                                <button class="quantity-btn" onclick="increaseQuantity(event, 'item5')">+</button>
                                <div class="remaining-qty" id="remaining5">Remaining: 1</div>
                            </div>
                            <div class="item-total">$19.00</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Split Preview Section -->
            <div class="split-preview">
                <div class="section-header">
                    <div class="section-title">
                        <span>📄</span>
                        Split Preview
                    </div>
                </div>

                <div class="new-table-card">
                    <h3>🆕 New Table: 5-S1</h3>
                    <p>Selected items will create a new separate bill</p>
                </div>

                <div class="split-items" id="splitItemsList">
                    <div class="empty-state">
                        <div class="empty-icon">🍽️</div>
                        <p>Select items to see split preview</p>
                    </div>
                </div>

                <div class="totals-card" id="splitTotals" style="display: none;">
                    <div class="total-row">
                        <span class="total-label">Subtotal:</span>
                        <span class="total-amount" id="splitSubtotal">$0.00</span>
                    </div>
                   
                </div>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="successModal">
        <div class="modal-content">
            <div class="success-icon">✅</div>
            <div class="modal-title">Bill Split Successful!</div>
            <div class="modal-details">
                Your bill has been successfully split into a new table.<br><br>
                <strong>New Table:</strong> Table 5-S1<br>
                <strong>Items Split:</strong> <span id="splitItemsCount">0</span> items<br>
                <strong>New Bill Total:</strong> $<span id="newBillTotal">0.00</span>
            </div>
            <button class="btn btn-success" onclick="closeSuccessModal()">
                <span>👍</span> Continue
            </button>
        </div>
    </div>

    <script>
        let selectedItems = [];
        let splitTotal = 0;

        const itemData = {
            'item1': { name: 'Grilled Chicken Breast', price: 18.50, maxQty: 4 },
            'item2': { name: 'Caesar Salad', price: 12.00, maxQty: 2 },
            'item3': { name: 'Pasta Carbonara', price: 16.50, maxQty: 3 },
            'item4': { name: 'House Wine', price: 8.00, maxQty: 6 },
            'item5': { name: 'Chocolate Dessert', price: 9.50, maxQty: 2 }
        };

        function toggleItem(element, itemId) {
            const checkbox = document.getElementById(`checkbox${itemId.slice(-1)}`);
            const controls = document.getElementById(`controls${itemId.slice(-1)}`);
            
            const isSelected = selectedItems.includes(itemId);
            
            if (isSelected) {
                // Deselect
                selectedItems = selectedItems.filter(id => id !== itemId);
                element.classList.remove('selected');
                checkbox.classList.remove('checked');
                controls.classList.remove('show');
            } else {
                // Select
                selectedItems.push(itemId);
                element.classList.add('selected');
                checkbox.classList.add('checked');
                controls.classList.add('show');
                updateRemainingQuantity(itemId);
            }
            
            updateSplitPreview();
            updateConfirmButton();
            updateSelectAllButton();
        }

        function selectAllItems() {
            const items = document.querySelectorAll('.menu-item');
            const isAllSelected = selectedItems.length === items.length;
            
            if (isAllSelected) {
                // Deselect all
                items.forEach((item, index) => {
                    const itemId = `item${index + 1}`;
                    const checkbox = document.getElementById(`checkbox${index + 1}`);
                    const controls = document.getElementById(`controls${index + 1}`);
                    
                    item.classList.remove('selected');
                    checkbox.classList.remove('checked');
                    controls.classList.remove('show');
                });
                selectedItems = [];
            } else {
                // Select all
                selectedItems = [];
                items.forEach((item, index) => {
                    const itemId = `item${index + 1}`;
                    const checkbox = document.getElementById(`checkbox${index + 1}`);
                    const controls = document.getElementById(`controls${index + 1}`);
                    
                    selectedItems.push(itemId);
                    item.classList.add('selected');
                    checkbox.classList.add('checked');
                    controls.classList.add('show');
                    updateRemainingQuantity(itemId);
                });
            }
            
            updateSplitPreview();
            updateConfirmButton();
            updateSelectAllButton();
        }

        function clearSelection() {
            const items = document.querySelectorAll('.menu-item');
            items.forEach((item, index) => {
                const checkbox = document.getElementById(`checkbox${index + 1}`);
                const controls = document.getElementById(`controls${index + 1}`);
                
                item.classList.remove('selected');
                checkbox.classList.remove('checked');
                controls.classList.remove('show');
            });
            selectedItems = [];
            updateSplitPreview();
            updateConfirmButton();
            updateSelectAllButton();
        }

        function updateRemainingQuantity(itemId) {
            const qtyInput = document.getElementById(`qty${itemId.slice(-1)}`);
            const remainingQty = document.getElementById(`remaining${itemId.slice(-1)}`);
            const maxQty = itemData[itemId].maxQty;
            const selectedQty = parseInt(qtyInput.value) || 1;
            remainingQty.textContent = `Remaining: ${maxQty - selectedQty}`;
            
            qtyInput.max = maxQty - (selectedItems.filter(id => id !== itemId).reduce((sum, id) => sum + (parseInt(document.getElementById(`qty${id.slice(-1)}`).value) || 0), 0));
        }

        function increaseQuantity(event, itemId) {
            event.stopPropagation();
            const qtyInput = document.getElementById(`qty${itemId.slice(-1)}`);
            let qty = parseInt(qtyInput.value);
            const maxQty = parseInt(qtyInput.max);
            if (qty < maxQty) {
                qtyInput.value = qty + 1;
                updateRemainingQuantity(itemId);
                updateSplitPreview();
            }
        }

        function decreaseQuantity(event, itemId) {
            event.stopPropagation();
            const qtyInput = document.getElementById(`qty${itemId.slice(-1)}`);
            let qty = parseInt(qtyInput.value);
            if (qty > 1) {
                qtyInput.value = qty - 1;
                updateRemainingQuantity(itemId);
                updateSplitPreview();
            }
        }

        function updateSplitPreview() {
            const splitItemsList = document.getElementById('splitItemsList');
            const splitTotals = document.getElementById('splitTotals');
            splitItemsList.innerHTML = '';

            if (selectedItems.length === 0) {
                splitItemsList.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-icon">🍽️</div>
                        <p>Select items to see split preview</p>
                    </div>
                `;
                splitTotals.style.display = 'none';
                return;
            }

            selectedItems.forEach(itemId => {
                const qtyInput = document.getElementById(`qty${itemId.slice(-1)}`);
                const qty = parseInt(qtyInput.value) || 1;
                const item = itemData[itemId];
                const total = (item.price * qty).toFixed(2);

                const splitItem = document.createElement('div');
                splitItem.className = 'split-item';
                splitItem.innerHTML = `
                    <div class="split-item-info">
                        <div class="split-item-name">${item.name}</div>
                        <div class="split-item-details">${qty} x $${item.price.toFixed(2)} each</div>
                    </div>
                    <div class="split-item-total">$${total}</div>
                `;
                splitItemsList.appendChild(splitItem);
            });

            splitTotal = selectedItems.reduce((sum, itemId) => {
                const qtyInput = document.getElementById(`qty${itemId.slice(-1)}`);
                const qty = parseInt(qtyInput.value) || 1;
                return sum + (itemData[itemId].price * qty);
            }, 0);

            const vat = splitTotal * 0.10;
            const grandTotal = splitTotal + vat;

            document.getElementById('splitSubtotal').textContent = `$${splitTotal.toFixed(2)}`;
            document.getElementById('splitVat').textContent = `$${vat.toFixed(2)}`;
            document.getElementById('splitGrandTotal').textContent = `$${grandTotal.toFixed(2)}`;
            splitTotals.style.display = 'block';
        }

        function updateConfirmButton() {
            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.disabled = selectedItems.length === 0;
        }

        function updateSelectAllButton() {
            const selectAllBtn = document.getElementById('selectAllBtn');
            const items = document.querySelectorAll('.menu-item');
            const isAllSelected = selectedItems.length === items.length;
            selectAllBtn.textContent = isAllSelected ? '☑️ Deselect All' : '☑️ Select All';
        }

        function confirmSplit() {
            if (selectedItems.length === 0) return;

            const modal = document.getElementById('successModal');
            document.getElementById('splitItemsCount').textContent = selectedItems.length;
            document.getElementById('newBillTotal').textContent = document.getElementById('splitGrandTotal').textContent;
            modal.classList.add('show');

            // Simulate clearing selection after confirmation (for demo purposes)
            setTimeout(() => {
                clearSelection();
                modal.classList.remove('show');
            }, 3000);
        }

        function closeSuccessModal() {
            const modal = document.getElementById('successModal');
            modal.classList.remove('show');
        }

        // Initialize event listeners for quantity inputs
        selectedItems.forEach(itemId => updateRemainingQuantity(itemId));
        document.querySelectorAll('.split-quantity').forEach(input => {
            input.addEventListener('change', (e) => {
                const itemId = `item${e.target.id.slice(-1)}`;
                const maxQty = parseInt(e.target.max);
                if (e.target.value > maxQty) e.target.value = maxQty;
                if (e.target.value < 1) e.target.value = 1;
                updateRemainingQuantity(itemId);
                updateSplitPreview();
            });
        });
    </script>
</body>
</html>