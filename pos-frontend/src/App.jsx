import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
  Navigate,
} from "react-router-dom";
import { Home, Auth, Orders, Tables, Menu, Dashboard, Reports, KOT, Payment } from "./pages";
import ChecksReport from "./pages/ChecksReport";
import Inventory from "./pages/Inventory";
import StockReports from "./pages/StockReports";
import Header from "./components/shared/Header";
import { useSelector } from "react-redux";
import useLoadData from "./hooks/useLoadData";
import FullScreenLoader from "./components/shared/FullScreenLoader"

function Layout() {
  const isLoading = useLoadData();
  const location = useLocation();
  const hideHeaderRoutes = ["/auth", "/orders", "/menu", "/kot", "/tables", "/payment", "/checks-report"];

  const { isAuth, role } = useSelector(state => state.user);

  if (isLoading) return <FullScreenLoader />

  return (
    <>
      {!hideHeaderRoutes.includes(location.pathname) && <Header />}
      <Routes>
        <Route
          path="/"
          element={
            <AdminProtectedRoutes>
              <Home />
            </AdminProtectedRoutes>
          }
        />
        <Route path="/auth" element={
          isAuth ? (
            role === "administrator" ? <Navigate to="/" /> : <Navigate to="/orders" />
          ) : (
            <Auth />
          )
        } />
        <Route
          path="/orders"
          element={
            <ProtectedRoutes>
              <Orders />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/tables"
          element={
            <ProtectedRoutes>
              <Tables />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/kot"
          element={
            <ProtectedRoutes>
              <KOT />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/menu"
          element={
            <ProtectedRoutes>
              <Menu />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/payment"
          element={
            <ProtectedRoutes>
              <Payment />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/checks-report"
          element={
            <ProtectedRoutes>
              <ChecksReport />
            </ProtectedRoutes>
          }
        />
        <Route
          path="/dashboard"
          element={
            <AdminProtectedRoutes>
              <Dashboard />
            </AdminProtectedRoutes>
          }
        />
        <Route
          path="/reports"
          element={
            <AdminProtectedRoutes>
              <Reports />
            </AdminProtectedRoutes>
          }
        />
        <Route
          path="/inventory"
          element={
            <AdminProtectedRoutes>
              <Inventory />
            </AdminProtectedRoutes>
          }
        />
        <Route
          path="/stock-reports"
          element={
            <AdminProtectedRoutes>
              <StockReports />
            </AdminProtectedRoutes>
          }
        />
        <Route path="*" element={<div>Not Found</div>} />
      </Routes>
    </>
  );
}

function ProtectedRoutes({ children }) {
  const { isAuth } = useSelector((state) => state.user);
  if (!isAuth) {
    return <Navigate to="/auth" />;
  }

  return children;
}

function AdminProtectedRoutes({ children }) {
  const { isAuth, role } = useSelector((state) => state.user);
  if (!isAuth) {
    return <Navigate to="/auth" />;
  }

  if (role !== "administrator") {
    return <Navigate to="/orders" />;
  }

  return children;
}

function App() {
  return (
    <Router>
      <Layout />
    </Router>
  );
}

export default App;
