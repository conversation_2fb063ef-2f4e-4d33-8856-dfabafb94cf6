import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { getGeneralSettings } from "../https";
import { setGeneralSettings } from "../redux/slices/generalSlice";

const useLoadData = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    getGeneralSettings()
      .then((res) => {
        const data = res.data?.data;
        dispatch(setGeneralSettings({
          defaultCurrency: data?.defaultCurrency || "GH₵",
          backgroundImage: data?.backgroundImage || "",
          companyName: data?.companyName || "",
          numberOfOrdersPerPage: data?.numberOfOrdersPerPage || 15,
          mode:data.mode,
          companyWebsite:data.companyWebsite,
          companyAddress:data.companyAddress,
          companyPhone:data.companyPhone,
          companyEmail:data.companyEmail


        }));
      })
      .finally(() => setLoading(false));
  }, [dispatch]);

  return loading;
};

export default useLoadData;
