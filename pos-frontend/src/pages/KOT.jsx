import React, { useEffect, useRef, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { axiosWrapper } from "../https/axiosWrapper";
import BackButton from "../components/shared/BackButton";
import POSBottomNav from "../components/shared/POSBottomNav";
import { enqueueSnackbar } from "notistack";

const WS_URL = `${import.meta.env.VITE_BACKEND_URL}/ws/orders/`;

// Audio context and beep management
let audioContext = null;
let isAudioInitialized = false;

const initializeAudio = () => {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
    if (audioContext.state === 'suspended') {
        audioContext.resume();
    }
    isAudioInitialized = true;
};

const beep = () => {
    if (!isAudioInitialized || !audioContext) {
        console.log('Audio not initialized yet');
        return;
    }

    try {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(880, audioContext.currentTime);

        // Connect oscillator to gain node to destination
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // Set volume
        gainNode.gain.setValueAtTime(1.0, audioContext.currentTime);

        oscillator.start();
        setTimeout(() => {
            oscillator.stop();
        }, 120);
    } catch (error) {
        console.error('Error playing beep:', error);
    }
};

const KOT = () => {
    const queryClient = useQueryClient();
    const wsRef = useRef(null);
    const [audioReady, setAudioReady] = useState(false);

    useEffect(() => {
        document.title = "POS | Kitchen Order Ticket";
    }, []);

    // Initialize audio on first user interaction
    useEffect(() => {
        const handleFirstInteraction = () => {
            initializeAudio();
            setAudioReady(true);

            // Remove listeners after first interaction
            document.removeEventListener('click', handleFirstInteraction);
            document.removeEventListener('keydown', handleFirstInteraction);
            document.removeEventListener('touchstart', handleFirstInteraction);
        };

        document.addEventListener('click', handleFirstInteraction);
        document.addEventListener('keydown', handleFirstInteraction);
        document.addEventListener('touchstart', handleFirstInteraction);

        return () => {
            document.removeEventListener('click', handleFirstInteraction);
            document.removeEventListener('keydown', handleFirstInteraction);
            document.removeEventListener('touchstart', handleFirstInteraction);
        };
    }, []);

    // WebSocket connection
    useEffect(() => {
        const ws = new window.WebSocket(WS_URL);
        wsRef.current = ws;

        ws.onopen = () => {
            console.log("WebSocket connected");
        };
        ws.onmessage = (event) => {
            enqueueSnackbar("New order received!", { variant: "success" });
            beep(); // This will now work after user interaction
            queryClient.invalidateQueries(["kot-orders"]);
        };
        ws.onerror = (err) => {
            console.error("WebSocket error", err);
        };
        ws.onclose = () => {
            console.log("WebSocket closed");
        };
        return () => {
            ws.close();
        };
    }, [queryClient]);

    // Fetch orders with opened status
    const { data: kotData, isLoading, error } = useQuery({
        queryKey: ["kot-orders"],
        queryFn: async () => {
            return await axiosWrapper.get("/api/orders/kot-orders/");
        },
        refetchInterval: false,
    });

    // Mark order as prepared mutation
    const markAsPreparedMutation = useMutation({
        mutationFn: async (orderId) => {
            return await axiosWrapper.patch(`/api/orders/${orderId}/kot-orders/`);
        },
        onSuccess: () => {
            enqueueSnackbar("Order marked as Done", { variant: "success" });
            queryClient.invalidateQueries(["kot-orders"]);
        },
        onError: (error) => {
            enqueueSnackbar("Failed to mark order as Done", { variant: "error" });
            console.error("Error marking order as Done:", error);
        }
    });

    const handleCardClick = (orderId) => {
        markAsPreparedMutation.mutate(orderId);
    };

    // Helper function to get card background color based on order delay
    const getCardBackgroundColor = (orderCreatedAt) => {
        const appSettingData = kotData?.data?.data?.appSetting || kotData?.data?.appSetting || kotData?.appSetting;
        if (!appSettingData?.orderDelay) {
            return "bg-[#262626]";
        }

        const orderDelay = appSettingData.orderDelay;

        const now = new Date();
        const orderTime = new Date(orderCreatedAt);
        const timeDiffMinutes = (now - orderTime) / (1000 * 60); // Convert to minutes

        if (timeDiffMinutes <= orderDelay) {
            return "bg-white"; // White background
        } else if (timeDiffMinutes <= orderDelay * 2) {
            return "bg-yellow-300"; // Medium yellow background
        } else if (timeDiffMinutes <= orderDelay * 3) {
            return "bg-orange-400"; // Medium orange background
        } else {
            return "bg-red-400"; // Medium red background
        }
    };

    // Helper function to get text color based on background
    const getTextColor = (orderCreatedAt) => {
        const appSettingData = kotData?.data?.data?.appSetting || kotData?.data?.appSetting || kotData?.appSetting;
        if (!appSettingData?.orderDelay) return "text-[#f5f5f5]";

        const orderDelay = appSettingData.orderDelay;
        const now = new Date();
        const orderTime = new Date(orderCreatedAt);
        const timeDiffMinutes = (now - orderTime) / (1000 * 60);

        if (timeDiffMinutes <= orderDelay) {
            return "text-black"; // Black text on white background
        } else {
            return "text-black"; // Black text on colored backgrounds for better contrast
        }
    };

    if (isLoading) {
        return (
            <div className="bg-[#1f1f1f] h-screen flex items-center justify-center">
                <div className="text-white text-xl">Loading orders...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="bg-[#1f1f1f] h-screen flex items-center justify-center">
                <div className="text-red-500 text-xl">Error loading orders</div>
            </div>
        );
    }

    // Process orders data to extract order items
    // Handle the actual nested structure: kotData.data.data.orders
    const orders = kotData?.data?.data?.orders || kotData?.data?.orders || kotData?.orders || [];
    const appSetting = kotData?.data?.data?.appSetting || kotData?.data?.appSetting || kotData?.appSetting;

    return (
        <section className="bg-[#1f1f1f] h-[calc(100vh-5rem)] flex flex-col">
            <div className="flex items-center justify-between px-4 sm:px-8 py-4 w-full">
                <div className="flex items-center gap-4">
                    <BackButton />
                    <h1 className="text-[#f5f5f5] text-2xl font-bold tracking-wider">
                        Kitchen Order Ticket
                    </h1>
                </div>
                <div className="flex items-center gap-4">
                    <div className="text-[#ababab] text-lg">
                        Pending Orders: {orders.length}
                    </div>
                    {appSetting?.orderDelay && (
                        <div className="text-[#ababab] text-sm">
                            Delay: {appSetting.orderDelay}min
                        </div>
                    )}
                    {!audioReady && (
                        <div className="bg-yellow-500 text-black px-2 py-1 rounded text-xs">
                            Click anywhere to enable sound
                        </div>
                    )}
                </div>
            </div>

            <div className="flex-1 w-full flex flex-col items-center">
                <div className="w-full flex-1 overflow-y-auto py-4 px-2 sm:px-4 bg-[#1f1f1f]">
                    {orders.length === 0 ? (
                        <div className="flex items-center justify-center h-full">
                            <div className="text-[#ababab] text-xl">No pending orders</div>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4 bg-[#1f1f1f]">
                            {orders.map((order) => (
                                <div
                                    key={order.id}
                                    onClick={() => handleCardClick(order.id)}
                                    className={`${getCardBackgroundColor(order.createdAt)} p-3 rounded-lg flex flex-col justify-between transition-colors duration-200 hover:opacity-80 cursor-pointer`}
                                >
                                    <div className="flex items-center justify-between mb-3">
                                        <h1 className={`${getTextColor(order.createdAt)} text-xl font-semibold`}>
                                            {order.table?.name || "Unknown Table"}
                                        </h1>
                                        <p className="text-yellow-400 bg-yellow-500/20 px-3 py-1 rounded text-sm font-medium">
                                            {order.status}
                                        </p>
                                    </div>

                                    {/* Order Items List */}
                                    <div className="space-y-2 mb-3">
                                        {order.orderItems?.map((item) => (
                                            <div
                                                key={item.id}
                                                className="bg-[#383838] p-2 rounded"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <span className="text-[#f5f5f5] text-sm font-medium">
                                                        {item.article?.name || "Unknown Item"}
                                                    </span>
                                                </div>
                                                <div className="mt-1">
                                                    <span className="text-[#ababab] text-xs">
                                                        Qty: <span className="text-[#f5f5f5]">{item.quantity}</span>
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>

                                    <div className="border-t border-[#444] pt-2">
                                        <p className={`${getTextColor(order.createdAt)} text-xs mt-1`}>
                                            By: <span className="font-medium">
                                                {order.createdBy?.firstName && order.createdBy?.lastName
                                                    ? `${order.createdBy.firstName} ${order.createdBy.lastName}`
                                                    : order.createdBy?.code || "Unknown"
                                                }
                                            </span>
                                        </p>
                                        <p className={`${getTextColor(order.createdAt)} text-xs mt-1`}>
                                            Time: <span className="font-medium">
                                                {new Date(order.createdAt).toLocaleTimeString()}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            <POSBottomNav />
        </section>
    );
};

export default KOT;