import React, { useState, useEffect } from "react";
// import POSBottomNav from "../components/shared/POSBottomNav";
// import BackButton from "../components/shared/BackButton";
import { keepPreviousData, useQuery, useMutation, useIsFetching, useQueryClient } from "@tanstack/react-query";
import { getOrders, logout } from "../https/index";
import { enqueueSnackbar } from "notistack";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { removeUser } from "../redux/slices/userSlice";
import { clearTable } from "../redux/slices/tableSlice";
import { clearCart, exitEditMode } from "../redux/slices/cartSlice";
import { setTable } from "../redux/slices/tableSlice";
import BottomNav from "../components/shared/BottomNav";
import PageNav from "../components/shared/PageNav";
import FullScreenLoader from '../components/shared/FullScreenLoader';
import { axiosWrapper } from "../https/axiosWrapper";

const Orders = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { defaultCurrency, numberOfOrdersPerPage } = useSelector((state) => state.general);
  const { _id: userId } = useSelector((state) => state.user);
  // New filter states based on the updated status model
  const [filter, setFilter] = useState("My");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(numberOfOrdersPerPage || 15);
  const [showRevenueModal, setShowRevenueModal] = useState(false);
  const [revenueCenters, setRevenueCenters] = useState([]);
  const [loadingRevenue, setLoadingRevenue] = useState(false);
  const [revenueError, setRevenueError] = useState("");
  const [selectedRevenue, setSelectedRevenue] = useState(() => {
    try {
      return JSON.parse(localStorage.getItem("revenueCenter"));
    } catch {
      return null;
    }
  });
  const [pendingRevenue, setPendingRevenue] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [forceRevenueModal, setForceRevenueModal] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    if (numberOfOrdersPerPage) setPageSize(numberOfOrdersPerPage);
  }, [numberOfOrdersPerPage]);

  useEffect(() => {
    document.title = "POS | Orders";
  }, []);

  // Live clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // On mount, force modal if no revenue center is set
  useEffect(() => {
    if (!selectedRevenue) {
      setShowRevenueModal(true);
      setForceRevenueModal(true);
      handleRevenueCenterClick();
    }
  }, []); // Only on mount

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: () => logout(),
    onSuccess: () => {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      dispatch(removeUser());
      dispatch(clearCart());
      dispatch(clearTable());
      enqueueSnackbar("Logged out successfully!", { variant: "success" });
      navigate("/auth");
    },
    onError: (error) => {
      console.log("Logout API failed:", error);
      // Even if logout API fails, still log out locally
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      dispatch(removeUser());
      dispatch(clearCart());
      dispatch(clearTable());
      enqueueSnackbar("Logged out successfully!", { variant: "success" });
      navigate("/auth");
    },
  });

  const handleLogout = () => {
    logoutMutation.mutate();
  };

  const handleNewCheck = () => {
    dispatch(exitEditMode());
    dispatch(clearTable());
    navigate("/menu", { replace: true, state: null });
  };

  const handleFastTransaction = () => {
    dispatch(exitEditMode());
    dispatch(clearTable());

    // Create a default table for fast transaction
    const tableId = `table_${Date.now()}`;
    const tableCode = `CHK_${Math.random().toString(36).slice(2, 10).toUpperCase()}`;
    const tableName = `${Math.floor(10000000 + Math.random() * 90000000)}`

    const defaultTable = {
      id: tableId,
      name: tableName,
      code: tableCode,
      phone: "",
      guests: 1,
      tableId: tableId
    };
    dispatch(setTable(defaultTable));
    navigate("/menu", { replace: true, state: null });
  };

  const handleRevenueCenterClick = async () => {
    setShowRevenueModal(true);
    setLoadingRevenue(true);
    setRevenueError("");
    try {
      const res = await axiosWrapper.get("/api/literals/category-list/?category=revenue_center");
      setRevenueCenters(res.data.data || []);
    } catch (e) {
      setRevenueError("Failed to load revenue centers");
    } finally {
      setLoadingRevenue(false);
    }
  };

  // When a revenue center is clicked
  const handleSelectRevenue = (center) => {
    if (selectedRevenue && selectedRevenue.id !== center.id) {
      setPendingRevenue(center);
      setShowConfirmModal(true);
    } else {
      setSelectedRevenue(center);
      localStorage.setItem("revenueCenter", JSON.stringify(center));
      setShowRevenueModal(false);
      setForceRevenueModal(false);
      enqueueSnackbar(`Revenue Center set: ${center.name}`, { variant: "success" });
      queryClient.invalidateQueries(["orders-page"]);
    }
  };

  // Confirm change
  const confirmChangeRevenue = () => {
    if (pendingRevenue) {
      setSelectedRevenue(pendingRevenue);
      localStorage.setItem("revenueCenter", JSON.stringify(pendingRevenue));
      setShowRevenueModal(false);
      setShowConfirmModal(false);
      setForceRevenueModal(false);
      enqueueSnackbar(`Revenue Center set: ${pendingRevenue.name}`, { variant: "success" });
      setPendingRevenue(null);
      queryClient.invalidateQueries(["orders-page"]);
    }
  };
  // Cancel change
  const cancelChangeRevenue = () => {
    setShowConfirmModal(false);
    setPendingRevenue(null);
  };

  // Build params for getOrders based on new status values
  // printed_and_sent_orders: true,
  let params = { paginate: false, include_voided: false };
  if (selectedRevenue && selectedRevenue.id) {
    params.revenueCenter = selectedRevenue.id;
  }
  if (filter === "All") {
    params.statuses = ["Printed", "Sent", "Prepared"];
  } else if (filter === "My") {
    params.statuses = ["Printed", "Sent", "Prepared"];
    // Add created_by filter for logged in user
    if (userId) {
      params.created_by = userId;
    }
  } else if (filter === "Functions Manager") {
    // Navigate to checks report page
    navigate("/checks-report");
    return;
  }

  const { data: resData, isError } = useQuery({
    queryKey: ["orders-page", filter],
    queryFn: async () => {
      return await getOrders(params);
    },
    placeholderData: keepPreviousData,
  });

  const isOrdersFetching = useIsFetching({ queryKey: ['orders-page', filter] }) > 0;

  if (isError) {
    enqueueSnackbar("Something went wrong!", { variant: "error" });
  }

  const handleOrderClick = (order) => {
    // Allow clicking on orders that are not Paid (can still be modified)
    if (order.status !== "Paid") {
      // Navigate to menu with order data
      navigate("/menu", {
        state: {
          orderId: order.id,
          orderItems: order.orderItems,
          table: order.table,
          totalAmount: order.totalAmount,
          discount: order.discount,
          grandTotal: order.grandTotal
        }
      });
    } else {
      enqueueSnackbar("Cannot modify paid orders", { variant: "warning" });
    }
  };

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Printed":
        return "text-blue-400 bg-blue-500/20";
      case "Sent":
        return "text-yellow-400 bg-yellow-500/20";
      case "Prepared":
        return "text-green-400 bg-green-500/20";
      case "Paid":
        return "text-purple-400 bg-purple-500/20";
      default:
        return "text-gray-400 bg-gray-500/20";
    }
  };

  // TableCard-like UI for orders
  const OrderGridCard = ({ order }) => {
    const isPaid = filter === "Paid";
    const tableCode = order.table?.code || "N/A";
    const tableName = order.table?.name || "N/A";
    const amount = order.totalAmount || "0.00";
    const isEmpty = order.isEmpty || false ? "(empty)" : null;


    // Format creator name in camel case
    const formatCreatorName = () => {
      const { firstName = "", lastName = "" } = order.createdBy || {};
      return firstName && lastName ? `${firstName} ${lastName}` : "Unknown";
    };

    const creatorName = formatCreatorName();
    // Format date/time (12-hour with AM/PM)
    let createdAt = order.createdAt || order.created_at;

    let formattedDate = "";
    if (createdAt) {
      const d = new Date(createdAt);
      let hours = d.getHours();
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      formattedDate = `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${hours}:${minutes} ${ampm}`;
    }



    return (
      <div
        key={order.id}
        onClick={() => handleOrderClick(order)}
        className="bg-[#232323] rounded-2xl shadow-lg border border-gray-800 p-6 flex flex-col items-center transition-all duration-200 hover:shadow-2xl hover:-translate-y-1 cursor-pointer min-h-[220px] relative"
      >
        {/* Amount */}
        <div className="text-2xl font-extrabold text-white mb-2 text-center">{defaultCurrency}{Number(amount).toFixed(2)}</div>
        {/* Table Name */}
        <div className="text-lg font-bold text-white mb-1 text-center truncate w-full">{tableName} {isEmpty}</div>
        {/* Code */}
        <div className="font-mono text-xs text-gray-400 bg-[#18181b] px-2 py-1 rounded mb-2 text-center truncate w-full">{tableCode}</div>
        {/* Status badge */}
        <div className={`absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(order.status)}`}>{order.status}</div>
        {/* Waiter/Employee (no initials avatar) */}
        <div className="flex flex-col mt-4 w-full">
          <span className="font-semibold text-white leading-tight">{creatorName}</span>
        </div>
        {/* Created date/time */}
        {formattedDate && (
          <div className="w-full text-right mt-3">
            <span className="text-xs text-gray-500">{formattedDate}</span>
          </div>
        )}
      </div>
    );
  };

  // Responsive columns
  let columns = 1;
  if (window.innerWidth >= 1536) columns = 5;
  else if (window.innerWidth >= 1280) columns = 4;
  else if (window.innerWidth >= 1024) columns = 4;
  else if (window.innerWidth >= 768) columns = 3;
  else if (window.innerWidth >= 640) columns = 2;

  // Calculate navigation state
  const hasPrev = page > 0;
  const hasNext = (page + 1) * pageSize < (resData?.data?.data?.length || 0);

  // Dynamically adjust page size to fill the grid with order cards + nav cards, with no empty slots before nav cards
  const maxCardsPerPage = columns * Math.floor(pageSize / columns);
  let dynamicPageSize = maxCardsPerPage - ((hasPrev || hasNext) ? 1 : 0);
  if (dynamicPageSize < 1) dynamicPageSize = 1;
  let startIdx = page * dynamicPageSize;
  let endIdx = startIdx + dynamicPageSize;
  let pagedOrders = resData?.data?.data?.slice(startIdx, endIdx) || [];

  // If there are empty slots before nav card and more orders, fill them
  let totalGridItems = pagedOrders.length + ((hasPrev || hasNext) ? 1 : 0);
  let remainder = totalGridItems % columns;
  while (remainder !== 0 && endIdx < (resData?.data?.data?.length || 0)) {
    pagedOrders.push(resData?.data?.data?.[endIdx]);
    endIdx++;
    totalGridItems = pagedOrders.length + ((hasPrev || hasNext) ? 1 : 0);
    remainder = totalGridItems % columns;
  }

  // Render grid
  const orderCards = pagedOrders.map(order => order ? <OrderGridCard order={order} key={order.id} /> : null).filter(Boolean);
  const gridItems = [...orderCards];
  if (hasPrev || hasNext) {
    gridItems.push(
      <PageNav
        key="page-nav"
        onPageUp={() => setPage(prev => (prev > 0 ? prev - 1 : 0))}
        onPageDown={() => setPage(prev => prev + 1)}
        disableUp={!hasPrev}
        disableDown={!hasNext}
      />
    );
  }
  if (gridItems.length % columns !== 0) {
    for (let i = 0; i < columns - (gridItems.length % columns); i++) {
      gridItems.push(<div key={`empty-${i}`} />);
    }
  }

  return (
    <section className="bg-[#1f1f1f] h-[calc(100vh-5rem)] flex flex-col">
      {isOrdersFetching && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-black bg-opacity-40">
          <FullScreenLoader />
          <div className="mt-6 text-white text-lg font-semibold tracking-wide">Refreshing orders, please wait...</div>
        </div>
      )}
      <div className="flex items-center justify-between px-4 sm:px-8 py-4 w-full">
        <div className="flex items-center gap-4"> </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setFilter("My")}
            className={`text-[#ababab] text-sm rounded-lg px-3 py-2 font-semibold transition-all duration-150 ${filter === "My" ? "bg-[#232b3b] border border-blue-500 text-blue-300" : "border border-transparent"}`}
          >
            MY CHECKS
          </button>
          <button
            onClick={() => setFilter("All")}
            className={`text-[#ababab] text-sm rounded-lg px-3 py-2 font-semibold transition-all duration-150 ${filter === "All" ? "bg-[#232b3b] border border-blue-500 text-blue-300" : "border border-transparent"}`}
          >
            ALL CHECKS
          </button>

          <button
            onClick={handleRevenueCenterClick}
            className={`text-[#ababab] text-sm rounded-lg px-3 py-2 font-semibold ml-2 border border-transparent`}
            disabled={forceRevenueModal}
          >
            REVENUE CENTER
          </button>

          <button
            onClick={() => setFilter("Functions Manager")}
            className={`text-[#ababab] text-sm rounded-lg px-3 py-2 font-semibold transition-all duration-150 ${filter === "Functions Manager" ? "bg-[#232b3b] border border-blue-500 text-blue-300" : "border border-transparent"}`}
          >
            FUNCTIONS MANAGER
          </button>

          <div className="text-[#ababab] text-lg ml-4">Orders: {resData?.data?.data?.length || 0}</div>
        </div>
      </div>

      <div className="flex-1 w-full flex flex-col items-center">
        <div className="w-full flex-1 overflow-y-auto py-4 px-2 sm:px-4 bg-[#1f1f1f]">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-4 bg-[#1f1f1f]">
            {gridItems}
          </div>
        </div>
      </div>

      {/* Custom 3-Button Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 flex flex-col z-50 shadow-lg">
        {/* Main button row */}
        <div className="flex items-center justify-between px-3 py-3 gap-3 h-20">
          {/* NEW CHECK - Long button (main focus) */}
          <button
            onClick={handleNewCheck}
            className="bg-blue-600 hover:bg-blue-500 active:bg-blue-700 text-white font-bold px-4 py-4 rounded-xl flex-[2] mx-1 text-xl transition-all duration-100 transform active:scale-95"
          >
            NEW CHECK
          </button>

          {/* FAST TRANSACTION */}
          <button
            onClick={handleFastTransaction}
            className="bg-green-600 hover:bg-green-500 active:bg-green-700 text-white font-bold px-4 py-4 rounded-xl flex-1 mx-1 text-xl transition-all duration-100 transform active:scale-95"
          >
            FAST TRANSACTION
          </button>

          {/* SIGN OUT */}
          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-500 active:bg-red-700 text-white font-bold px-4 py-4 rounded-xl flex-1 mx-1 text-xl transition-all duration-100 transform active:scale-95"
          >
            SIGN OUT
          </button>
        </div>

        {/* Status bar with ticking clock */}
        <div className="bg-gray-800 text-gray-300 text-sm px-4 py-2 flex justify-between items-center h-6">
          <BottomNav />
        </div>
      </div>

      {/* Revenue Center Modal */}
      {showRevenueModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-[#232323] rounded-xl p-12 w-[38rem] max-w-full flex flex-col items-center">
            <h2 className="text-2xl font-bold text-white mb-4">Select Revenue Center</h2>
            {loadingRevenue ? (
              <div className="text-white">Loading...</div>
            ) : revenueError ? (
              <div className="text-red-400">{revenueError}</div>
            ) : (
              <ul className="w-full space-y-2">
                {revenueCenters.map(center => (
                  <li key={center.id}>
                    <button
                      className={`w-full text-left px-4 py-3 rounded bg-[#18181b] text-white hover:bg-[#444] ${selectedRevenue?.id === center.id ? "ring-2 ring-blue-400" : ""}`}
                      onClick={() => handleSelectRevenue(center)}
                    >
                      <div className="font-bold">{center.name}</div>
                      <div className="text-sm text-gray-400">{center.description}</div>
                    </button>
                  </li>
                ))}
              </ul>
            )}
            {!forceRevenueModal && (
              <button className="mt-6 bg-gray-500 text-white px-8 py-3 rounded-lg font-bold text-lg" onClick={() => setShowRevenueModal(false)}>Close</button>
            )}
          </div>
        </div>
      )}
      {/* Confirm Change Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-[#232323] rounded-xl p-8 w-[28rem] flex flex-col items-center">
            <h2 className="text-2xl font-bold text-white mb-4">Change Revenue Center?</h2>
            <div className="text-white mb-6">Are you sure you want to change the revenue center to <span className="font-bold text-blue-400">{pendingRevenue?.name}</span>?</div>
            <div className="flex gap-6">
              <button className="bg-gray-500 text-white px-8 py-3 rounded-lg font-bold text-lg" onClick={cancelChangeRevenue}>Cancel</button>
              <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-bold text-lg" onClick={confirmChangeRevenue}>Confirm</button>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default Orders;
