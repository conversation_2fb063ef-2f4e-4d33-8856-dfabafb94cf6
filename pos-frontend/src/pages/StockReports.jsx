import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  IoBarChart, 
  IoTrendingUp, 
  IoTrendingDown, 
  IoCalendar,
  IoFilter,
  IoDownload,
  IoSearch,
  IoList,
  IoGrid,
  IoTime,
  IoWarning
} from 'react-icons/io5';
import { axiosWrapper } from '../https/axiosWrapper';
import StockMovementsTable from '../components/inventory/StockMovementsTable';
import StockBreakdownChart from '../components/inventory/StockBreakdownChart';
import StockTransactionFilters from '../components/inventory/StockTransactionFilters';

const StockReports = () => {
  const [activeTab, setActiveTab] = useState('movements');
  const [filters, setFilters] = useState({
    date_range_preset: 'last_7_days',
    movement_type: '',
    article_id: '',
    start_date: '',
    end_date: '',
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('table');

  // Fetch stock movements
  const { data: stockMovements, isLoading: movementsLoading, refetch: refetchMovements } = useQuery({
    queryKey: ['stock-movements', filters, searchTerm],
    queryFn: () => {
      const params = { ...filters };
      if (searchTerm) params.search = searchTerm;
      return axiosWrapper.get('/api/inventory/movements/', { params });
    },
    staleTime: 30000,
  });

  // Fetch inventory items for breakdown
  const { data: inventoryItems, isLoading: itemsLoading } = useQuery({
    queryKey: ['inventory-items-breakdown'],
    queryFn: () => axiosWrapper.get('/api/inventory/items/'),
    staleTime: 60000,
  });

  // Fetch movement analytics
  const { data: movementAnalytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ['stock-movement-analytics', filters],
    queryFn: () => {
      const params = { ...filters };
      return axiosWrapper.get('/api/inventory/movements/analytics/', { params });
    },
    staleTime: 30000,
  });

  const tabs = [
    { id: 'movements', label: 'Stock Movements', icon: IoList },
    { id: 'breakdown', label: 'Stock Breakdown', icon: IoBarChart },
    { id: 'analytics', label: 'Analytics', icon: IoTrendingUp },
  ];

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleExport = () => {
    const movements = Array.isArray(stockMovements) ? stockMovements : [];
    if (movements.length === 0) {
      alert('No data to export');
      return;
    }

    // Create CSV content
    const headers = [
      'Date',
      'Item',
      'Movement Type',
      'Quantity',
      'Unit Cost',
      'Total Value',
      'Batch',
      'Reference',
      'Notes'
    ];

    const csvContent = [
      headers.join(','),
      ...movements.map(movement => [
        new Date(movement.created_at).toLocaleString(),
        `"${movement.inventory_item?.article?.name || 'Unknown'}"`,
        movement.movement_type,
        movement.quantity,
        movement.unit_cost,
        movement.total_value,
        `"${movement.batch?.batch_number || 'N/A'}"`,
        `"${movement.reference_number || 'N/A'}"`,
        `"${movement.notes || 'N/A'}"`
      ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `stock_movements_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate summary stats from movements
  const getSummaryStats = () => {
    // Handle case where stockMovements might be undefined or not an array
    const movements = Array.isArray(stockMovements) ? stockMovements : [];

    if (movements.length === 0) {
      return {
        totalMovements: 0,
        totalIncoming: 0,
        totalOutgoing: 0,
        netChange: 0
      };
    }

    const totalMovements = movements.length;
    const totalIncoming = movements
      .filter(m => m.quantity > 0)
      .reduce((sum, m) => sum + parseFloat(m.quantity), 0);
    const totalOutgoing = movements
      .filter(m => m.quantity < 0)
      .reduce((sum, m) => sum + Math.abs(parseFloat(m.quantity)), 0);
    const netChange = totalIncoming - totalOutgoing;

    return {
      totalMovements,
      totalIncoming,
      totalOutgoing,
      netChange
    };
  };

  const summaryStats = getSummaryStats();

  return (
    <div className="min-h-screen bg-[#1a1a1a] text-[#f5f5f5]">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-[#f5f5f5] mb-2">
              Stock Reports & Analytics
            </h1>
            <p className="text-[#ababab]">
              Track stock movements, analyze trends, and monitor inventory performance
            </p>
          </div>
          
          <div className="flex items-center gap-3 mt-4 lg:mt-0">
            <button
              onClick={handleExport}
              className="flex items-center gap-2 bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors"
            >
              <IoDownload className="text-lg" />
              Export
            </button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-[#262626] rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#ababab] text-sm">Total Movements</p>
                <p className="text-2xl font-bold text-[#f5f5f5]">
                  {summaryStats.totalMovements}
                </p>
              </div>
              <IoList className="text-3xl text-blue-500" />
            </div>
          </div>

          <div className="bg-[#262626] rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#ababab] text-sm">Stock In</p>
                <p className="text-2xl font-bold text-green-500">
                  {summaryStats.totalIncoming.toFixed(2)}
                </p>
              </div>
              <IoTrendingUp className="text-3xl text-green-500" />
            </div>
          </div>

          <div className="bg-[#262626] rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#ababab] text-sm">Stock Out</p>
                <p className="text-2xl font-bold text-red-500">
                  {summaryStats.totalOutgoing.toFixed(2)}
                </p>
              </div>
              <IoTrendingDown className="text-3xl text-red-500" />
            </div>
          </div>

          <div className="bg-[#262626] rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#ababab] text-sm">Net Change</p>
                <p className={`text-2xl font-bold ${
                  summaryStats.netChange >= 0 ? 'text-green-500' : 'text-red-500'
                }`}>
                  {summaryStats.netChange >= 0 ? '+' : ''}{summaryStats.netChange.toFixed(2)}
                </p>
              </div>
              <IoBarChart className={`text-3xl ${
                summaryStats.netChange >= 0 ? 'text-green-500' : 'text-red-500'
              }`} />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-[#262626] rounded-lg p-4 mb-6">
          <StockTransactionFilters
            filters={filters}
            onFilterChange={handleFilterChange}
          />
        </div>

        {/* Tabs */}
        <div className="flex items-center gap-1 mb-6 bg-[#262626] rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-[#ababab] hover:text-[#f5f5f5] hover:bg-[#333]'
                }`}
              >
                <Icon className="text-lg" />
                {tab.label}
              </button>
            );
          })}
        </div>

        {/* Search and View Controls */}
        {activeTab === 'movements' && (
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <IoSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#ababab]" />
              <input
                type="text"
                placeholder="Search stock movements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-[#262626] border border-[#404040] rounded-lg text-[#f5f5f5] placeholder-[#ababab] focus:outline-none focus:border-blue-500"
              />
            </div>
            
            <div className="flex items-center gap-1 bg-[#262626] rounded-lg p-1">
              <button
                onClick={() => setViewMode('table')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'table' ? 'bg-blue-600 text-white' : 'text-[#ababab] hover:text-[#f5f5f5]'
                }`}
              >
                <IoList />
              </button>
              <button
                onClick={() => setViewMode('chart')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'chart' ? 'bg-blue-600 text-white' : 'text-[#ababab] hover:text-[#f5f5f5]'
                }`}
              >
                <IoBarChart />
              </button>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="bg-[#262626] rounded-lg">
          {activeTab === 'movements' && (
            <>
              {viewMode === 'table' ? (
                <StockMovementsTable
                  movements={stockMovements?.data.data}
                  loading={movementsLoading}
                />
              ) : (
                <StockBreakdownChart
                  movements={stockMovements?.data.data}
                  loading={movementsLoading}
                  type="movements"
                />
              )}
            </>
          )}

          {activeTab === 'breakdown' && (
            <StockBreakdownChart
              items={inventoryItems?.data.data}
              loading={itemsLoading}
              type="breakdown"
            />
          )}

          {activeTab === 'analytics' && (
            <StockBreakdownChart
              analytics={movementAnalytics?.data}
              loading={analyticsLoading}
              type="analytics"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default StockReports;
