@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', sans-serif;
  @apply bg-white text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* FullScreenLoader.css */

.fullscreen-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  @apply bg-white dark:bg-gray-900;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999; /* Ensure it's on top of other content */
}

.spinner {
  @apply border-4 border-gray-300 dark:border-gray-600;
  border-top: 4px solid #f6b100; /* Orange color for spinner */
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}