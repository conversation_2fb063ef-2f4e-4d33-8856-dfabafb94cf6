import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

const ThemeProvider = ({ children }) => {
  const darkMode = useSelector((state) => state.general.darkMode);

  useEffect(() => {
    // Apply or remove the 'dark' class to the document element
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Cleanup function to remove the class when component unmounts
    return () => {
      document.documentElement.classList.remove('dark');
    };
  }, [darkMode]);

  return <>{children}</>;
};

export default ThemeProvider;
