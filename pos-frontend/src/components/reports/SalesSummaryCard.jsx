import React from "react";
import { 
  IoReceipt, 
  IoCash, 
  IoTrendingUp, 
  IoBasket,
  IoTime,
  IoCalendar
} from "react-icons/io5";

const SalesSummaryCard = ({ data, isLoading, currency = "GH₵" }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-[#262626] rounded-lg p-6 animate-pulse">
            <div className="flex items-center justify-between mb-4">
              <div className="w-8 h-8 bg-gray-600 rounded"></div>
              <div className="w-16 h-4 bg-gray-600 rounded"></div>
            </div>
            <div className="w-24 h-8 bg-gray-600 rounded mb-2"></div>
            <div className="w-32 h-4 bg-gray-600 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!data) {
    return (
      <div className="bg-[#262626] rounded-lg p-6 text-center">
        <p className="text-[#ababab]">No sales data available</p>
      </div>
    );
  }

  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  const summaryCards = [
    {
      title: "Total Revenue",
      value: formatCurrency(data.totalRevenue),
      icon: <IoCash className="text-2xl" />,
      color: "bg-green-600",
      textColor: "text-green-100",
      description: "Total sales revenue"
    },
    {
      title: "Total Orders",
      value: formatNumber(data.totalOrders),
      icon: <IoReceipt className="text-2xl" />,
      color: "bg-blue-600",
      textColor: "text-blue-100",
      description: "Number of completed orders"
    },
    {
      title: "Average Order Value",
      value: formatCurrency(data.avgOrderValue),
      icon: <IoTrendingUp className="text-2xl" />,
      color: "bg-purple-600",
      textColor: "text-purple-100",
      description: "Average value per order"
    },
    {
      title: "Items Sold",
      value: formatNumber(data.totalItemsSold),
      icon: <IoBasket className="text-2xl" />,
      color: "bg-orange-600",
      textColor: "text-orange-100",
      description: "Total items sold"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryCards.map((card, index) => (
          <div
            key={index}
            className={`${card.color} rounded-lg p-6 text-white shadow-lg hover:shadow-xl transition-shadow`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg bg-white bg-opacity-20 ${card.textColor}`}>
                {card.icon}
              </div>
              <div className="text-right">
                <p className="text-xs opacity-80 uppercase tracking-wide">
                  {card.title}
                </p>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="text-2xl font-bold">{card.value}</h3>
              <p className="text-sm opacity-80">{card.description}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Metrics */}
      {data.period_days && (
        <div className="bg-[#262626] rounded-lg p-6">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoCalendar className="text-xl" />
            Period Analysis
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-[#f5f5f5]">{data.period_days}</p>
              <p className="text-sm text-[#ababab]">Days in Period</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-[#f5f5f5]">
                {formatCurrency((data.total_revenue || 0) / (data.period_days || 1))}
              </p>
              <p className="text-sm text-[#ababab]">Average Daily Revenue</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-[#f5f5f5]">
                {formatNumber((data.total_orders || 0) / (data.period_days || 1))}
              </p>
              <p className="text-sm text-[#ababab]">Average Daily Orders</p>
            </div>
          </div>
        </div>
      )}

      {/* Generated At */}
      {data.generated_at && (
        <div className="text-center">
          <p className="text-xs text-[#ababab] flex items-center justify-center gap-1">
            <IoTime className="text-sm" />
            Last updated: {new Date(data.generated_at).toLocaleString()}
          </p>
        </div>
      )}
    </div>
  );
};

export default SalesSummaryCard;
