import React, { useState } from "react";
import {
  IoPeople,
  IoRestaurant,
  IoDesktop,
  IoTrendingUp,
  IoChevronDown,
  IoChevronUp
} from "react-icons/io5";

const PerformanceSection = ({
  userPerformance,
  tablePerformance,
  workstationPerformance,
  isLoading,
  currency = "GH₵"
}) => {
  const [activeTab, setActiveTab] = useState("users");
  const [showAll, setShowAll] = useState({
    users: false,
    tables: false,
    workstations: false
  });

  const formatCurrency = (amount) => {
    // Handle custom currency symbols like GH₵
    if (currency === "GH₵" || currency.includes("₵")) {
      return `${currency} ${new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
      }).format(amount || 0)}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency === "GH₵" ? "USD" : currency,
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  const formatNumber = (number) => {
    return new Intl.NumberFormat('en-US').format(number || 0);
  };

  const toggleShowAll = (type) => {
    setShowAll(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };

  const getDisplayData = (data, type) => {
    if (!data || data.length === 0) return [];
    return showAll[type] ? data : data.slice(0, 5);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="w-64 h-6 bg-gray-600 rounded animate-pulse"></div>
        <div className="bg-[#262626] rounded-lg p-6 animate-pulse">
          <div className="flex gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="w-24 h-8 bg-gray-600 rounded"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between p-3 bg-gray-700 rounded">
                <div className="w-32 h-4 bg-gray-600 rounded"></div>
                <div className="w-16 h-4 bg-gray-600 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Extract data arrays from API responses
  const userDataArray = userPerformance?.data?.data || [];
  const tableDataArray = tablePerformance?.data?.data || [];
  const workstationDataArray = workstationPerformance?.data?.data || [];

  const tabs = [
    { id: "users", label: "User Performance", icon: <IoPeople />, data: userDataArray },
    { id: "tables", label: "Table Performance", icon: <IoRestaurant />, data: tableDataArray },
    { id: "workstations", label: "Workstation Performance", icon: <IoDesktop />, data: workstationDataArray }
  ];

  const renderUserRow = (user, index) => (
    <div key={user.userId || user.id || index} className="flex items-center justify-between p-4 bg-[#1a1a1a] rounded-lg hover:bg-[#333] transition-colors">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {index + 1}
        </div>
        <div>
          <p className="text-[#f5f5f5] font-medium">
            {user.firstName || user.first_name} {user.lastName || user.last_name}
          </p>
          <p className="text-[#ababab] text-sm">@{user.username}</p>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4 text-right">
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatNumber(user.totalOrders || user.total_orders)}</p>
          <p className="text-[#ababab] text-xs">Orders</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(user.totalRevenue || user.total_revenue)}</p>
          <p className="text-[#ababab] text-xs">Revenue</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(user.avgOrderValue || user.avg_order_value)}</p>
          <p className="text-[#ababab] text-xs">Avg Order</p>
        </div>
      </div>
    </div>
  );

  const renderTableRow = (table, index) => (
    <div key={table.tableId || table.table_id || table.id || index} className="flex items-center justify-between p-4 bg-[#1a1a1a] rounded-lg hover:bg-[#333] transition-colors">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {index + 1}
        </div>
        <div>
          <p className="text-[#f5f5f5] font-medium">
            {table.tableName || table.table_name || table.name}
          </p>
          <p className="text-[#ababab] text-sm">Code: {table.tableCode || table.table_code || table.code}</p>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4 text-right">
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatNumber(table.totalOrders || table.total_orders)}</p>
          <p className="text-[#ababab] text-xs">Orders</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(table.totalRevenue || table.total_revenue)}</p>
          <p className="text-[#ababab] text-xs">Revenue</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(table.avgOrderValue || table.avg_order_value)}</p>
          <p className="text-[#ababab] text-xs">Avg Order</p>
        </div>
      </div>
    </div>
  );

  const renderWorkstationRow = (workstation, index) => (
    <div key={workstation.workstationId || workstation.workstation_id || workstation.id || index} className="flex items-center justify-between p-4 bg-[#1a1a1a] rounded-lg hover:bg-[#333] transition-colors">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
          {index + 1}
        </div>
        <div>
          <p className="text-[#f5f5f5] font-medium">
            {workstation.workstationName || workstation.workstation_name || workstation.name}
          </p>
          <p className="text-[#ababab] text-sm">
            {(workstation.workstationLocation || workstation.workstation_location || workstation.location) &&
             `Location: ${workstation.workstationLocation || workstation.workstation_location || workstation.location}`}
          </p>
        </div>
      </div>
      <div className="grid grid-cols-3 gap-4 text-right">
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatNumber(workstation.totalOrders || workstation.total_orders)}</p>
          <p className="text-[#ababab] text-xs">Orders</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(workstation.totalRevenue || workstation.total_revenue)}</p>
          <p className="text-[#ababab] text-xs">Revenue</p>
        </div>
        <div>
          <p className="text-[#f5f5f5] font-bold">{formatCurrency(workstation.avgOrderValue || workstation.avg_order_value)}</p>
          <p className="text-[#ababab] text-xs">Avg Order</p>
        </div>
      </div>
    </div>
  );

  const activeTabData = tabs.find(tab => tab.id === activeTab);
  const displayData = getDisplayData(activeTabData?.data || [], activeTab);

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-[#f5f5f5] flex items-center gap-2">
        <IoTrendingUp className="text-2xl" />
        Performance Analytics
      </h2>

      <div className="bg-[#262626] rounded-lg p-6">
        {/* Tab Navigation */}
        <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-600 pb-4">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? "bg-blue-600 text-white"
                  : "bg-[#1a1a1a] text-[#ababab] hover:bg-[#333] hover:text-[#f5f5f5]"
              }`}
            >
              {tab.icon}
              {tab.label}
              <span className="bg-black bg-opacity-20 px-2 py-1 rounded text-xs">
                {tab.data?.length || 0}
              </span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="space-y-3">
          {displayData.length === 0 ? (
            <p className="text-[#ababab] text-center py-8">No performance data available</p>
          ) : (
            <>
              {activeTab === "users" && displayData.map(renderUserRow)}
              {activeTab === "tables" && displayData.map(renderTableRow)}
              {activeTab === "workstations" && displayData.map(renderWorkstationRow)}
              
              {/* Show More/Less Button */}
              {activeTabData?.data && activeTabData.data.length > 5 && (
                <button
                  onClick={() => toggleShowAll(activeTab)}
                  className="w-full flex items-center justify-center gap-2 p-3 bg-[#1a1a1a] hover:bg-[#333] text-[#ababab] hover:text-[#f5f5f5] rounded-lg transition-colors"
                >
                  {showAll[activeTab] ? (
                    <>
                      <IoChevronUp />
                      Show Less
                    </>
                  ) : (
                    <>
                      <IoChevronDown />
                      Show All ({activeTabData.data.length - 5} more)
                    </>
                  )}
                </button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default PerformanceSection;
