import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  IoCalendar, 
  IoFilter, 
  IoRefresh,
  IoCheckmarkCircle,
  IoClose
} from 'react-icons/io5';
import { axiosWrapper } from '../../https/axiosWrapper';

const StockTransactionFilters = ({ filters, onFilterChange }) => {
  const [showCustomDates, setShowCustomDates] = useState(false);

  // Fetch articles for filtering
  const { data: articlesResponse } = useQuery({
    queryKey: ['articles-for-filter'],
    queryFn: () => axiosWrapper.get('/api/products/articles/?paginate=false'),
    staleTime: 300000, // 5 minutes
  });

  // Extract articles array from response
  const articles = articlesResponse?.data?.data || [];

  const datePresets = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 Days' },
    { value: 'last_30_days', label: 'Last 30 Days' },
    { value: 'this_week', label: 'This Week' },
    { value: 'last_week', label: 'Last Week' },
    { value: 'this_month', label: 'This Month' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'this_quarter', label: 'This Quarter' },
    { value: 'last_quarter', label: 'Last Quarter' },
    { value: 'this_year', label: 'This Year' },
    { value: 'last_year', label: 'Last Year' },
    { value: 'custom', label: 'Custom Range' }
  ];

  const movementTypes = [
    { value: '', label: 'All Types' },
    { value: 'Purchase', label: 'Purchase' },
    { value: 'Sale', label: 'Sale' },
    { value: 'Adjustment', label: 'Adjustment' },
    { value: 'Waste', label: 'Waste' },
    { value: 'Transfer', label: 'Transfer' },
    { value: 'Expiry', label: 'Expiry' }
  ];

  const handleDatePresetChange = (preset) => {
    if (preset === 'custom') {
      setShowCustomDates(true);
      onFilterChange({ 
        date_range_preset: preset,
        start_date: '',
        end_date: ''
      });
    } else {
      setShowCustomDates(false);
      onFilterChange({ 
        date_range_preset: preset,
        start_date: '',
        end_date: ''
      });
    }
  };

  const handleCustomDateChange = (field, value) => {
    onFilterChange({ [field]: value });
  };

  const handleReset = () => {
    setShowCustomDates(false);
    onFilterChange({
      date_range_preset: 'last_7_days',
      movement_type: '',
      article_id: '',
      start_date: '',
      end_date: '',
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.movement_type) count++;
    if (filters.article_id) count++;
    if (filters.start_date && filters.end_date) count++;
    return count;
  };

  return (
    <div className="space-y-4">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <IoFilter className="text-xl text-blue-500" />
          <h3 className="text-lg font-medium text-[#f5f5f5]">
            Stock Transaction Filters
          </h3>
          {getActiveFiltersCount() > 0 && (
            <span className="px-2 py-1 bg-blue-500 text-white text-xs rounded-full">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        
        <button
          onClick={handleReset}
          className="flex items-center gap-2 px-3 py-1 bg-[#404040] hover:bg-[#555] text-[#f5f5f5] rounded text-sm transition-colors"
        >
          <IoRefresh className="text-sm" />
          Reset
        </button>
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Date Range Preset */}
        <div>
          <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
            <IoCalendar className="inline mr-1" />
            Date Range
          </label>
          <select
            value={filters.date_range_preset}
            onChange={(e) => handleDatePresetChange(e.target.value)}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#404040] rounded-lg text-[#f5f5f5] focus:outline-none focus:border-blue-500"
          >
            {datePresets.map((preset) => (
              <option key={preset.value} value={preset.value}>
                {preset.label}
              </option>
            ))}
          </select>
        </div>

        {/* Movement Type */}
        <div>
          <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
            Movement Type
          </label>
          <select
            value={filters.movement_type}
            onChange={(e) => onFilterChange({ movement_type: e.target.value })}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#404040] rounded-lg text-[#f5f5f5] focus:outline-none focus:border-blue-500"
          >
            {movementTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Article Filter */}
        <div>
          <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
            Article
          </label>
          <select
            value={filters.article_id}
            onChange={(e) => onFilterChange({ article_id: e.target.value })}
            className="w-full px-3 py-2 bg-[#1a1a1a] border border-[#404040] rounded-lg text-[#f5f5f5] focus:outline-none focus:border-blue-500"
          >
            <option value="">All Articles</option>
            {Array.isArray(articles) && articles.map((article) => (
              <option key={article.id} value={article.id}>
                {article.name}
              </option>
            ))}
          </select>
        </div>

        {/* Quick Actions */}
        <div>
          <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
            Quick Filters
          </label>
          <div className="flex gap-2">
            <button
              onClick={() => onFilterChange({ movement_type: 'Purchase' })}
              className={`px-3 py-2 rounded text-xs transition-colors ${
                filters.movement_type === 'Purchase'
                  ? 'bg-green-600 text-white'
                  : 'bg-[#404040] text-[#ababab] hover:bg-[#555]'
              }`}
            >
              Purchases
            </button>
            <button
              onClick={() => onFilterChange({ movement_type: 'Sale' })}
              className={`px-3 py-2 rounded text-xs transition-colors ${
                filters.movement_type === 'Sale'
                  ? 'bg-red-600 text-white'
                  : 'bg-[#404040] text-[#ababab] hover:bg-[#555]'
              }`}
            >
              Sales
            </button>
          </div>
        </div>
      </div>

      {/* Custom Date Range */}
      {showCustomDates && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-[#1a1a1a] rounded-lg border border-[#404040]">
          <div>
            <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={filters.start_date}
              onChange={(e) => handleCustomDateChange('start_date', e.target.value)}
              className="w-full px-3 py-2 bg-[#262626] border border-[#404040] rounded-lg text-[#f5f5f5] focus:outline-none focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#f5f5f5] mb-2">
              End Date
            </label>
            <input
              type="date"
              value={filters.end_date}
              onChange={(e) => handleCustomDateChange('end_date', e.target.value)}
              className="w-full px-3 py-2 bg-[#262626] border border-[#404040] rounded-lg text-[#f5f5f5] focus:outline-none focus:border-blue-500"
            />
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {getActiveFiltersCount() > 0 && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-[#ababab]">Active filters:</span>
          
          {filters.movement_type && (
            <span className="flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">
              Type: {filters.movement_type}
              <button
                onClick={() => onFilterChange({ movement_type: '' })}
                className="hover:text-blue-300"
              >
                <IoClose className="text-xs" />
              </button>
            </span>
          )}
          
          {filters.article_id && (
            <span className="flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
              Article: {Array.isArray(articles) ? articles.find(a => a.id === filters.article_id)?.name || 'Selected' : 'Selected'}
              <button
                onClick={() => onFilterChange({ article_id: '' })}
                className="hover:text-green-300"
              >
                <IoClose className="text-xs" />
              </button>
            </span>
          )}
          
          {filters.start_date && filters.end_date && (
            <span className="flex items-center gap-1 px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs">
              Custom: {filters.start_date} to {filters.end_date}
              <button
                onClick={() => {
                  setShowCustomDates(false);
                  onFilterChange({ start_date: '', end_date: '', date_range_preset: 'last_7_days' });
                }}
                className="hover:text-purple-300"
              >
                <IoClose className="text-xs" />
              </button>
            </span>
          )}
        </div>
      )}

      {/* Filter Summary */}
      <div className="flex items-center gap-4 text-sm text-[#ababab]">
        <div className="flex items-center gap-1">
          <IoCheckmarkCircle className="text-green-500" />
          <span>Filters applied automatically</span>
        </div>
        <div>
          Date Range: {datePresets.find(p => p.value === filters.date_range_preset)?.label || 'Custom'}
        </div>
      </div>
    </div>
  );
};

export default StockTransactionFilters;
