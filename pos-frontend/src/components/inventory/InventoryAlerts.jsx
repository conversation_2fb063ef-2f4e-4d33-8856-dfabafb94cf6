import React, { useState } from 'react';
import {
  Io<PERSON><PERSON>ning,
  Io<PERSON>lert,
  IoTime,
  IoSkull,
  IoCheckmarkCircle,
  IoSettings,
  IoRefresh
} from 'react-icons/io5';

const InventoryAlerts = ({ alerts, loading }) => {
  const [activeTab, setActiveTab] = useState('low_stock');

  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="bg-[#1a1a1a] rounded-lg p-4 animate-pulse">
              <div className="w-full h-6 bg-gray-600 rounded mb-2"></div>
              <div className="w-3/4 h-4 bg-gray-600 rounded mb-2"></div>
              <div className="w-1/2 h-4 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!alerts || !alerts.data || !alerts.data.data) {
    return (
      <div className="p-6 text-center">
        <p className="text-[#ababab]">No alerts data available</p>
      </div>
    );
  }

  // Extract the actual alerts data from the nested structure
  const alertsData = alerts.data.data;

  const alertTabs = [
    { 
      id: 'low_stock', 
      label: 'Low Stock', 
      icon: IoWarning, 
      color: 'text-yellow-500',
      count: alertsData.lowStock?.length || 0
    },
    {
      id: 'out_of_stock',
      label: 'Out of Stock',
      icon: IoAlert,
      color: 'text-red-500',
      count: alertsData.outOfStock?.length || 0
    },
    {
      id: 'expiring_soon',
      label: 'Expiring Soon',
      icon: IoTime,
      color: 'text-orange-500',
      count: alertsData.expiringSoon?.length || 0
    },
    {
      id: 'expired',
      label: 'Expired',
      icon: IoSkull,
      color: 'text-red-600',
      count: alertsData.expired?.length || 0
    }
  ];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  const AlertCard = ({ item, type }) => {
    const getAlertConfig = () => {
      switch (type) {
        case 'low_stock':
          return {
            borderColor: 'border-yellow-500',
            bgColor: 'bg-yellow-500/10',
            icon: IoWarning,
            iconColor: 'text-yellow-500',
            title: 'Low Stock Alert',
            description: `Current: ${item.currentStock} | Reorder: ${item.reorderPoint}`
          };
        case 'out_of_stock':
          return {
            borderColor: 'border-red-500',
            bgColor: 'bg-red-500/10',
            icon: IoAlert,
            iconColor: 'text-red-500',
            title: 'Out of Stock',
            description: 'Immediate restocking required'
          };
        case 'expiring_soon':
          return {
            borderColor: 'border-orange-500',
            bgColor: 'bg-orange-500/10',
            icon: IoTime,
            iconColor: 'text-orange-500',
            title: 'Expiring Soon',
            description: `${item.expiringSoonStock} units expiring within 7 days`
          };
        case 'expired':
          return {
            borderColor: 'border-red-600',
            bgColor: 'bg-red-600/10',
            icon: IoSkull,
            iconColor: 'text-red-600',
            title: 'Expired Stock',
            description: `${item.expiredStock} units have expired`
          };
        default:
          return {
            borderColor: 'border-gray-500',
            bgColor: 'bg-gray-500/10',
            icon: IoCheckmarkCircle,
            iconColor: 'text-gray-500',
            title: 'Unknown',
            description: 'Unknown alert type'
          };
      }
    };

    const config = getAlertConfig();
    const AlertIcon = config.icon;

    return (
      <div className={`bg-[#1a1a1a] rounded-lg p-4 border-l-4 ${config.borderColor} ${config.bgColor}`}>
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <AlertIcon className={`text-xl ${config.iconColor}`} />
            <div>
              <h3 className="font-semibold text-[#f5f5f5]">
                {item.article?.name || 'Unknown Item'}
              </h3>
              <p className="text-sm text-[#ababab]">
                {item.article?.subFamily?.family?.name} - {item.article?.subFamily?.name}
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-2 mb-4">
          <div className="flex justify-between">
            <span className="text-[#ababab] text-sm">Alert Type:</span>
            <span className={`text-sm font-medium ${config.iconColor}`}>{config.title}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-[#ababab] text-sm">Current Stock:</span>
            <span className="text-[#f5f5f5] font-medium">{item.currentStock}</span>
          </div>
          {type === 'low_stock' && (
            <div className="flex justify-between">
              <span className="text-[#ababab] text-sm">Reorder Point:</span>
              <span className="text-[#f5f5f5]">{item.reorderPoint}</span>
            </div>
          )}
          <div className="flex justify-between">
            <span className="text-[#ababab] text-sm">Stock Value:</span>
            <span className="text-[#f5f5f5]">{formatCurrency(item.stockValue)}</span>
          </div>
        </div>

        <p className="text-sm text-[#ababab] mb-4">{config.description}</p>

        <div className="flex items-center gap-2">
          <button className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded text-sm transition-colors">
            <IoSettings className="text-sm" />
            Take Action
          </button>
          {type === 'expired' && (
            <button className="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 px-3 py-2 rounded text-sm transition-colors">
              <IoRefresh className="text-sm" />
              Mark as Waste
            </button>
          )}
        </div>
      </div>
    );
  };

  // Updated function to access nested data
  const getAlertsForTab = (tabId) => {
    switch (tabId) {
      case 'low_stock':
        return alertsData.lowStock || [];
      case 'out_of_stock':
        return alertsData.outOfStock || [];
      case 'expiring_soon':
        return alertsData.expiringSoon || [];
      case 'expired':
        return alertsData.expired || [];
      default:
        return [];
    }
  };

  const currentAlerts = getAlertsForTab(activeTab);

  return (
    <div className="p-6">
      {/* Alert Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {alertTabs.map((tab) => {
          const TabIcon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-[#1a1a1a] text-[#ababab] hover:text-[#f5f5f5] hover:bg-[#333]'
              }`}
            >
              <TabIcon className={`text-lg ${activeTab === tab.id ? 'text-white' : tab.color}`} />
              {tab.label}
              {tab.count > 0 && (
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  activeTab === tab.id 
                    ? 'bg-white text-blue-600' 
                    : 'bg-red-500 text-white'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          );
        })}
      </div>

      {/* Alert Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {alertTabs.map((tab) => {
          const TabIcon = tab.icon;
          return (
            <div key={tab.id} className="bg-[#1a1a1a] rounded-lg p-4 border border-[#404040]">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-[#ababab] text-sm">{tab.label}</p>
                  <p className={`text-2xl font-bold ${tab.color}`}>{tab.count}</p>
                </div>
                <TabIcon className={`text-3xl ${tab.color}`} />
              </div>
            </div>
          );
        })}
      </div>

      {/* Alert Cards */}
      {currentAlerts.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {currentAlerts.map((item) => (
            <AlertCard key={item.id} item={item} type={activeTab} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <IoCheckmarkCircle className="text-6xl text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-[#f5f5f5] mb-2">
            No {alertTabs.find(tab => tab.id === activeTab)?.label} Alerts
          </h3>
          <p className="text-[#ababab]">
            All items in this category are within normal parameters.
          </p>
        </div>
      )}
    </div>
  );
};

export default InventoryAlerts;