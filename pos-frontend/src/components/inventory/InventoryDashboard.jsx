import React from 'react';
import { 
  IoTrendingUp, 
  IoWarning, 
  IoTime, 
  IoAlert,
  IoCheckmarkCircle,
  IoArrowUp,
  IoArrowDown
} from 'react-icons/io5';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts';

const InventoryDashboard = ({ data, loading }) => {
  console.log('data', data);
  if (loading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-[#1a1a1a] rounded-lg p-6 animate-pulse">
              <div className="w-48 h-6 bg-gray-600 rounded mb-4"></div>
              <div className="w-full h-64 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-6 text-center">
        <p className="text-[#ababab]">No dashboard data available</p>
      </div>
    );
  }

  // Process recent movements for chart
  const movementData = data.data.recentMovements?.slice(0, 10).map((movement, index) => ({
    // Handle camelCase API response
    name: movement.inventoryItem?.article?.name || `Item ${index + 1}`,
    quantity: Math.abs(movement.quantity),
    type: movement.movementType,
    value: movement.totalValue
  })) || [];


  console.log('movementData',movementData)

  // Process low stock alerts for pie chart
  const alertsData = [
    { name: 'Normal Stock', value: data.data.totalItems - data.data.lowStockItems - data.data.outOfStockItems, color: '#10b981' },
    { name: 'Low Stock', value: data.data.lowStockItems, color: '#f59e0b' },
    { name: 'Out of Stock', value: data.data.outOfStockItems, color: '#ef4444' }
  ].filter(item => item.value > 0);

  const COLORS = ['#10b981', '#f59e0b', '#ef4444'];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#404040]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#ababab] text-sm">Total Items</p>
              <p className="text-2xl font-bold text-[#f5f5f5]">{data.data.totalItems}</p>
              <p className="text-green-500 text-xs flex items-center gap-1 mt-1">
                <IoArrowUp className="text-xs" />
                Active inventory
              </p>
            </div>
            <IoCheckmarkCircle className="text-3xl text-green-500" />
          </div>
        </div>

        <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#404040]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#ababab] text-sm">Stock Value</p>
              <p className="text-2xl font-bold text-[#f5f5f5]">
                {formatCurrency(data.data.totalStockValue)}
              </p>
              <p className="text-blue-500 text-xs flex items-center gap-1 mt-1">
                <IoTrendingUp className="text-xs" />
                Total investment
              </p>
            </div>
            <IoTrendingUp className="text-3xl text-blue-500" />
          </div>
        </div>

        <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#404040]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#ababab] text-sm">Low Stock Items</p>
              <p className="text-2xl font-bold text-yellow-500">{data.data.lowStockItems}</p>
              <p className="text-yellow-500 text-xs flex items-center gap-1 mt-1">
                <IoWarning className="text-xs" />
                Need attention
              </p>
            </div>
            <IoWarning className="text-3xl text-yellow-500" />
          </div>
        </div>

        <div className="bg-[#1a1a1a] rounded-lg p-4 border border-[#404040]">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#ababab] text-sm">Out of Stock</p>
              <p className="text-2xl font-bold text-red-500">{data.data.outOfStockItems}</p>
              <p className="text-red-500 text-xs flex items-center gap-1 mt-1">
                <IoAlert className="text-xs" />
                Urgent action
              </p>
            </div>
            <IoAlert className="text-3xl text-red-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Stock Movements */}
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#404040]">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoTime className="text-xl" />
            Recent Stock Movements
          </h3>
          {movementData.length > 0 ? (
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={movementData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                <XAxis 
                  dataKey="name" 
                  stroke="#ababab"
                  fontSize={12}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  stroke="#ababab"
                  fontSize={12}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: '#1a1a1a',
                    border: '1px solid #444',
                    borderRadius: '8px',
                    color: '#f5f5f5'
                  }}
                  formatter={(value, name) => [
                    name === 'quantity' ? `${value} units` : formatCurrency(value),
                    name === 'quantity' ? 'Quantity' : 'Value'
                  ]}
                />
                <Bar dataKey="quantity" fill="#3b82f6" name="quantity" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="h-64 flex items-center justify-center text-[#ababab]">
              No recent movements
            </div>
          )}
        </div>

        {/* Stock Status Distribution */}
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#404040]">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoAlert className="text-xl" />
            Stock Status Distribution
          </h3>
          {alertsData.length > 0 ? (
            <div className="flex items-center">
              <ResponsiveContainer width="60%" height={200}>
                <PieChart>
                  <Pie
                    data={alertsData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {alertsData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: '#1a1a1a',
                      border: '1px solid #444',
                      borderRadius: '8px',
                      color: '#f5f5f5'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
              
              <div className="flex-1 space-y-3">
                {alertsData.map((item, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    ></div>
                    <div className="flex-1">
                      <p className="text-sm text-[#f5f5f5]">{item.name}</p>
                      <p className="text-xs text-[#ababab]">{item.value} items</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="h-48 flex items-center justify-center text-[#ababab]">
              No stock data available
            </div>
          )}
        </div>
      </div>

      {/* Low Stock Alerts */}
      {data.data.lowStockAlerts && data.data.lowStockAlerts.length > 0 && (
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#404040]">
          <h3 className="text-lg font-semibold text-[#f5f5f5] mb-4 flex items-center gap-2">
            <IoWarning className="text-xl text-yellow-500" />
            Low Stock Alerts
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.data.lowStockAlerts.slice(0, 6).map((item) => (
              <div key={item.id} className="bg-[#262626] rounded-lg p-4 border-l-4 border-yellow-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-[#f5f5f5]">{item.article?.name}</p>
                    <p className="text-sm text-[#ababab]">
                      Current: {item.currentStock} | Reorder: {item.reorderPoint}
                    </p>
                  </div>
                  <IoArrowDown className="text-yellow-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pending Orders */}
      {data.data.pendingOrders > 0 && (
        <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#404040]">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-[#f5f5f5] flex items-center gap-2">
                <IoTime className="text-xl text-blue-500" />
                Pending Purchase Orders
              </h3>
              <p className="text-[#ababab] mt-1">
                {data.data.pendingOrders} orders awaiting approval or delivery
              </p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-500">{data.data.pendingOrders}</p>
              <p className="text-sm text-[#ababab]">Orders</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InventoryDashboard;
