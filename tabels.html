<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
        }

        .filters {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-label {
            font-size: 0.85rem;
            color: #64748b;
            font-weight: 500;
        }

        .filter-select, .search-input {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-select:focus, .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-input {
            min-width: 250px;
        }

        .transactions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .transaction-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .transaction-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .transaction-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .transaction-id {
            font-size: 1.2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .transaction-ref {
            font-size: 0.85rem;
            color: #64748b;
            font-family: 'Courier New', monospace;
            background: #f1f5f9;
            padding: 4px 8px;
            border-radius: 6px;
        }

        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-sent {
            background: linear-gradient(135deg, #fef3c7, #f59e0b);
            color: #92400e;
        }

        .status-prepared {
            background: linear-gradient(135deg, #dcfce7, #22c55e);
            color: #166534;
        }

        .amount {
            font-size: 2rem;
            font-weight: 800;
            color: #1e293b;
            margin: 15px 0;
            text-align: center;
        }

        .recipient {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 12px;
            margin-top: 15px;
        }

        .recipient-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .recipient-info {
            flex: 1;
        }

        .recipient-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 2px;
        }

        .recipient-label {
            font-size: 0.8rem;
            color: #64748b;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            flex: 1;
            padding: 12px;
            border-radius: 10px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #e2e8f0;
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .floating-action:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .transactions-grid {
                grid-template-columns: 1fr;
            }
            
            .filters {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-input {
                min-width: auto;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="dashboard">
       

        <div class="fade-in">
         
                <select class="filter-select" id="statusFilter">
                   
            </div>
            
            
            
            <div class="filter-group" style="flex: 1;">
                <!-- <label class="filter-label">Search</label> -->
                <input type="text" class="search-input" placeholder="Search by ID, reference, or recipient..." id="searchInput">
            </div>
        </div>

        <div class="transactions-grid" id="transactionsGrid">
            <!-- Transaction cards will be generated by JavaScript -->
        </div>
    </div>

    <button class="floating-action" onclick="addNewTransaction()">+</button>

    <script>
        const transactions = [
            { id: '51457537', ref: 'CHK_RSG76HL', amount: '100.00', status: 'sent', recipient: 'Emmanuel Walter' },
            { id: 'Test Flow 01', ref: 'CHK_3GADSW3', amount: '23.00', status: 'sent', recipient: 'Emmanuel Walter' },
            { id: '49892039', ref: 'CHK_2BAAMUH3', amount: '23.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'Lambda Table 1', ref: 'CHK_GHOZRT4T', amount: '31.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'Emmans Check-S1', ref: 'TBL4C4BF724', amount: '23.00', status: 'sent', recipient: 'Emmanuel Walter' },
            { id: 'Test Patch', ref: 'CHK_FE3BTRAX', amount: '69.00', status: 'sent', recipient: 'Emmanuel Walter' },
            { id: 'Emmans 01', ref: 'CHK_F23KRY86', amount: '157.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'TEST 3 ITEMS SEND', ref: 'CHK_I80J3ZAP', amount: '264.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'TEST UPDATE 02', ref: 'CHK_BDZ5M0AM', amount: '46.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'TEST UPDATE 01', ref: 'CHK_WM95E08B', amount: '46.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'Test Update', ref: 'CHK_G0M35Q5', amount: '115.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: '423-S2', ref: 'TBL4870E006', amount: '146.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: '423-S1', ref: 'TBLAEE3CBF8', amount: '180.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'qe', ref: 'CHK_MAM8J5S1', amount: '34.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: '3et', ref: 'CHK_H18XXJCE', amount: '0.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'qr2', ref: 'CHK_OMNPA48N', amount: '0.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: '32', ref: 'CHK_7155N22P', amount: '0.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'qr23', ref: 'CHK_DADKMJHJ', amount: '0.00', status: 'prepared', recipient: 'Emmanuel Walter' },
            { id: 'Table 223', ref: 'CHK_7T5NVR9U', amount: '0.00', status: 'prepared', recipient: 'Emmanuel Walter' }
        ];

        function getInitials(name) {
            return name.split(' ').map(n => n[0]).join('').toUpperCase();
        }

        function formatAmount(amount) {
            return `GH₵${parseFloat(amount).toFixed(2)}`;
        }

        function createTransactionCard(transaction) {
            return `
                <div class="transaction-card fade-in">
                   
                    
                    <div class="amount">${formatAmount(transaction.amount)}</div>
                    
                    <div class="recipient">
                        <div class="recipient-avatar">${getInitials(transaction.recipient)}</div>
                        <div class="recipient-info">
                            <div class="recipient-name">${transaction.recipient}</div>
                            <div class="recipient-label">Recipient</div>
                        </div>
                    </div>
                    
                   
                </div>
            `;
        }

        function renderTransactions(transactionsToRender = transactions) {
            const grid = document.getElementById('transactionsGrid');
            grid.innerHTML = transactionsToRender.map(createTransactionCard).join('');
        }

        function filterTransactions() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            let filtered = transactions;
            
            if (statusFilter) {
                filtered = filtered.filter(t => t.status === statusFilter);
            }
            
            if (searchTerm) {
                filtered = filtered.filter(t => 
                    t.id.toLowerCase().includes(searchTerm) ||
                    t.ref.toLowerCase().includes(searchTerm) ||
                    t.recipient.toLowerCase().includes(searchTerm)
                );
            }
            
            renderTransactions(filtered);
        }

        function viewDetails(id) {
            alert(`Viewing details for transaction: ${id}`);
        }

        function editTransaction(id) {
            alert(`Editing transaction: ${id}`);
        }

        function addNewTransaction() {
            alert('Add new transaction functionality would be implemented here');
        }

        // Event listeners
        document.getElementById('statusFilter').addEventListener('change', filterTransactions);
        document.getElementById('searchInput').addEventListener('input', filterTransactions);

        // Initial render
        renderTransactions();
    </script>
</body>
</html>