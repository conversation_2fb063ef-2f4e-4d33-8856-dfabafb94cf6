<svg width="1600" height="900" viewBox="0 0 1600 900" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <!-- Gradients -->
      <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#16213e;stop-opacity:1" />
      </linearGradient>
      
      <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:rgba(255,255,255,0.15);stop-opacity:1" />
        <stop offset="100%" style="stop-color:rgba(255,255,255,0.05);stop-opacity:1" />
      </linearGradient>
      
      <linearGradient id="buttonGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
        <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
      </linearGradient>
      
      <linearGradient id="drinkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#90EE90;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#32CD32;stop-opacity:0.8" />
      </linearGradient>
      
      <linearGradient id="foodGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FFD700;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#FFA500;stop-opacity:0.8" />
      </linearGradient>
      
      <linearGradient id="dessertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FF6B9D;stop-opacity:0.8" />
        <stop offset="100%" style="stop-color:#FF1744;stop-opacity:0.8" />
      </linearGradient>
      
      <linearGradient id="softDrinksBg" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#90EE90;stop-opacity:0.3" />
        <stop offset="100%" style="stop-color:#32CD32;stop-opacity:0.3" />
      </linearGradient>
      
      <linearGradient id="alcoholicBg" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#FF6384;stop-opacity:0.3" />
        <stop offset="100%" style="stop-color:#FF1744;stop-opacity:0.3" />
      </linearGradient>
      
      <!-- Filters -->
      <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur in="SourceGraphic" stdDeviation="10"/>
      </filter>
      
      <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
        <feDropShadow dx="0" dy="8" stdDeviation="15" flood-color="rgba(0,0,0,0.3)"/>
      </filter>
      
      <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
        <feMerge> 
          <feMergeNode in="coloredBlur"/>
          <feMergeNode in="SourceGraphic"/>
        </feMerge>
      </filter>
    </defs>
    
    <!-- Background -->
    <rect width="1600" height="900" fill="url(#bgGradient)"/>
    
    <!-- Header -->
    <rect x="0" y="0" width="1600" height="70" fill="rgba(255,255,255,0.1)" filter="url(#blur)"/>
    <rect x="0" y="0" width="1600" height="70" fill="rgba(255,255,255,0.05)"/>
    <line x1="0" y1="70" x2="1600" y2="70" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Logo -->
    <text x="25" y="45" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">⚡ Lambda Group</text>
    
    <!-- User Info -->
    <text x="1450" y="45" font-family="Arial, sans-serif" font-size="14" fill="white">Emmanuel Quarshie</text>
    <circle cx="1540" cy="35" r="20" fill="url(#buttonGradient)"/>
    <text x="1540" y="42" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">EQ</text>
    
    <!-- Left Sidebar -->
    <rect x="0" y="70" width="280" height="830" fill="rgba(0,0,0,0.2)"/>
    <line x1="280" y1="70" x2="280" y2="900" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Categories Title -->
    <text x="20" y="110" font-family="Arial, sans-serif" font-size="16" fill="rgba(255,255,255,0.8)">Categories</text>
    
    <!-- Number Grid -->
    <g id="numberGrid">
      <!-- Row 1 -->
      <rect x="20" y="130" width="45" height="45" rx="12" fill="url(#buttonGradient)" filter="url(#shadow)"/>
      <text x="42.5" y="158" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">1</text>
      
      <rect x="75" y="130" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="97.5" y="158" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">2</text>
      
      <rect x="130" y="130" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="152.5" y="158" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">3</text>
      
      <rect x="185" y="130" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="207.5" y="158" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">4</text>
      
      <rect x="240" y="130" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="262.5" y="158" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">5</text>
      
      <!-- Row 2 -->
      <rect x="20" y="185" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="42.5" y="213" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">6</text>
      
      <rect x="75" y="185" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="97.5" y="213" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">7</text>
      
      <rect x="130" y="185" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="152.5" y="213" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">8</text>
      
      <rect x="185" y="185" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="207.5" y="213" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">9</text>
      
      <rect x="240" y="185" width="45" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
      <text x="262.5" y="213" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white" text-anchor="middle">0</text>
    </g>
    
    <!-- Category Items -->
    <!-- Drinks (Active) -->
    <rect x="20" y="260" width="240" height="55" rx="12" fill="url(#drinkGradient)" filter="url(#glow)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="35" y="285" font-family="Arial, sans-serif" font-size="24">🥤</text>
    <text x="70" y="285" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">Drinks</text>
    <text x="70" y="302" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)">DRK</text>
    
    <!-- Food -->
    <rect x="20" y="325" width="240" height="55" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <text x="35" y="350" font-family="Arial, sans-serif" font-size="24">🍔</text>
    <text x="70" y="350" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">Food</text>
    <text x="70" y="367" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)">FOD</text>
    
    <!-- Desserts -->
    <rect x="20" y="390" width="240" height="55" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <text x="35" y="415" font-family="Arial, sans-serif" font-size="24">🍰</text>
    <text x="70" y="415" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">Desserts</text>
    <text x="70" y="432" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)">DST</text>
    
    <!-- Main Content Area -->
    <rect x="280" y="70" width="920" height="830" fill="rgba(0,0,0,0.1)"/>
    
    <!-- Search Bar -->
    <rect x="310" y="100" width="860" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <text x="330" y="128" font-family="Arial, sans-serif" font-size="16" fill="rgba(255,255,255,0.6)">🔍 Search products...</text>
    
    <!-- Subcategory Tabs -->
    <rect x="310" y="170" width="120" height="35" rx="17" fill="url(#softDrinksBg)" stroke="rgba(144,238,144,0.3)" stroke-width="1"/>
    <text x="370" y="192" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="#90EE90" text-anchor="middle">Soft Drinks</text>
    
    <rect x="445" y="170" width="100" height="35" rx="17" fill="url(#alcoholicBg)" stroke="rgba(255,99,132,0.3)" stroke-width="1"/>
    <text x="495" y="192" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="#FF6384" text-anchor="middle">Alcoholic</text>
    
    <!-- Product Cards -->
    <!-- Coca-Cola -->
    <rect x="310" y="230" width="280" height="120" rx="16" fill="url(#cardGradient)" filter="url(#shadow)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <rect x="310" y="230" width="280" height="4" rx="2" fill="url(#buttonGradient)"/>
    <circle cx="570" cy="250" r="4" fill="#00ff88" filter="url(#glow)"/>
    
    <text x="330" y="265" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Coca-Cola 500ml</text>
    <rect x="330" y="275" width="50" height="18" rx="6" fill="rgba(255,255,255,0.1)"/>
    <text x="355" y="287" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">COKE</text>
    <text x="330" y="325" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#4facfe">GH₵20.00</text>
    
    <!-- Fanta Orange -->
    <rect x="610" y="230" width="280" height="120" rx="16" fill="url(#cardGradient)" filter="url(#shadow)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <rect x="610" y="230" width="280" height="4" rx="2" fill="url(#buttonGradient)"/>
    <circle cx="870" cy="250" r="4" fill="#00ff88" filter="url(#glow)"/>
    
    <text x="630" y="265" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Fanta Orange 500ml</text>
    <rect x="630" y="275" width="55" height="18" rx="6" fill="rgba(255,255,255,0.1)"/>
    <text x="657.5" y="287" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">FANTA</text>
    <text x="630" y="325" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#4facfe">GH₵30.00</text>
    
    <!-- Heineken Beer -->
    <rect x="310" y="370" width="280" height="120" rx="16" fill="url(#cardGradient)" filter="url(#shadow)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <rect x="310" y="370" width="280" height="4" rx="2" fill="url(#buttonGradient)"/>
    <circle cx="570" cy="390" r="4" fill="#00ff88" filter="url(#glow)"/>
    
    <text x="330" y="405" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Heineken Beer 330ml</text>
    <rect x="330" y="415" width="45" height="18" rx="6" fill="rgba(255,255,255,0.1)"/>
    <text x="352.5" y="427" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">HEIN</text>
    <text x="330" y="465" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#4facfe">GH₵25.00</text>
    
    <!-- Sprite -->
    <rect x="610" y="370" width="280" height="120" rx="16" fill="url(#cardGradient)" filter="url(#shadow)" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    <rect x="610" y="370" width="280" height="4" rx="2" fill="url(#buttonGradient)"/>
    <circle cx="870" cy="390" r="4" fill="#00ff88" filter="url(#glow)"/>
    
    <text x="630" y="405" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Sprite 500ml</text>
    <rect x="630" y="415" width="50" height="18" rx="6" fill="rgba(255,255,255,0.1)"/>
    <text x="655" y="427" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">SPRITE</text>
    <text x="630" y="465" font-family="Arial, sans-serif" font-size="20" font-weight="700" fill="#4facfe">GH₵20.00</text>
    
    <!-- Right Panel (Order Details) -->
    <rect x="1200" y="70" width="400" height="830" fill="rgba(0,0,0,0.3)"/>
    <line x1="1200" y1="70" x2="1200" y2="900" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Order Header -->
    <text x="1225" y="105" font-family="Arial, sans-serif" font-size="18" font-weight="600" fill="white">Order Details</text>
    <circle cx="1555" cy="95" r="18" fill="url(#buttonGradient)"/>
    <text x="1555" y="102" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">CN</text>
    
    <!-- Customer Info -->
    <rect x="1225" y="125" width="350" height="60" rx="12" fill="rgba(255,255,255,0.1)"/>
    <text x="1245" y="150" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">#N/A / Dine in</text>
    <text x="1245" y="170" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)">June 17, 2025</text>
    
    <!-- Order Items -->
    <text x="1225" y="220" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white">Order Items</text>
    
    <!-- Coca-Cola Item -->
    <rect x="1225" y="235" width="350" height="70" rx="8" fill="rgba(255,255,255,0.05)"/>
    <text x="1245" y="255" font-family="Arial, sans-serif" font-size="16" font-weight="500" fill="white">Coca-Cola 500ml</text>
    <text x="1245" y="275" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.7)">GH₵20.00 each</text>
    
    <!-- Quantity Controls -->
    <circle cx="1485" cy="260" r="15" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="1485" y="266" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">-</text>
    <text x="1515" y="266" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">3</text>
    <circle cx="1545" cy="260" r="15" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="1545" y="266" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">+</text>
    
    <text x="1555" y="295" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#4facfe" text-anchor="end">GH₵60</text>
    
    <line x1="1225" y1="305" x2="1575" y2="305" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Fanta Item -->
    <rect x="1225" y="315" width="350" height="70" rx="8" fill="rgba(255,255,255,0.05)"/>
    <text x="1245" y="335" font-family="Arial, sans-serif" font-size="16" font-weight="500" fill="white">Fanta Orange 500ml</text>
    <text x="1245" y="355" font-family="Arial, sans-serif" font-size="14" fill="rgba(255,255,255,0.7)">GH₵30.00 each</text>
    
    <!-- Fanta Quantity Controls -->
    <circle cx="1485" cy="340" r="15" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="1485" y="346" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">-</text>
    <text x="1515" y="346" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">1</text>
    <circle cx="1545" cy="340" r="15" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="1545" y="346" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">+</text>
    
    <text x="1555" y="375" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="#4facfe" text-anchor="end">GH₵30</text>
    
    <!-- Order Summary -->
    <rect x="1225" y="420" width="350" height="120" rx="12" fill="rgba(255,255,255,0.1)"/>
    <text x="1245" y="445" font-family="Arial, sans-serif" font-size="14" fill="white">Items(2)</text>
    <text x="1555" y="445" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="end">GH₵90.00</text>
    
    <text x="1245" y="470" font-family="Arial, sans-serif" font-size="14" fill="white">Tax (0%)</text>
    <text x="1555" y="470" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="end">GH₵0.00</text>
    
    <line x1="1245" y1="485" x2="1555" y2="485" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <text x="1245" y="510" font-family="Arial, sans-serif" font-size="18" font-weight="700" fill="white">Total</text>
    <text x="1555" y="510" font-family="Arial, sans-serif" font-size="18" font-weight="700" fill="#4facfe" text-anchor="end">GH₵90.00</text>
    
    <text x="1245" y="530" font-family="Arial, sans-serif" font-size="14" fill="white">Cash</text>
    <text x="1555" y="530" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="end">Online</text>
    
    <!-- Action Buttons -->
    <rect x="1225" y="570" width="165" height="45" rx="12" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
    <text x="1307.5" y="598" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">Print Receipt</text>
    
    <rect x="1410" y="570" width="165" height="45" rx="12" fill="url(#buttonGradient)" filter="url(#shadow)"/>
    <text x="1492.5" y="598" font-family="Arial, sans-serif" font-size="16" font-weight="600" fill="white" text-anchor="middle">Place Order</text>
    
    <!-- Bottom Navigation -->
    <rect x="0" y="830" width="1600" height="70" fill="rgba(0,0,0,0.3)"/>
    <line x1="0" y1="830" x2="1600" y2="830" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    
    <!-- Nav Items -->
    <g id="nav-home">
      <rect x="350" y="845" width="80" height="40" rx="12" fill="url(#buttonGradient)"/>
      <text x="390" y="865" font-family="Arial, sans-serif" font-size="20" fill="white" text-anchor="middle">🏠</text>
      <text x="390" y="880" font-family="Arial, sans-serif" font-size="12" fill="white" text-anchor="middle">Home</text>
    </g>
    
    <g id="nav-orders">
      <text x="590" y="865" font-family="Arial, sans-serif" font-size="20" fill="rgba(255,255,255,0.7)" text-anchor="middle">📋</text>
      <text x="590" y="880" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">Orders</text>
    </g>
    
    <g id="nav-tables">
      <text x="990" y="865" font-family="Arial, sans-serif" font-size="20" fill="rgba(255,255,255,0.7)" text-anchor="middle">🍽️</text>
      <text x="990" y="880" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">Tables</text>
    </g>
    
    <g id="nav-more">
      <text x="1190" y="865" font-family="Arial, sans-serif" font-size="20" fill="rgba(255,255,255,0.7)" text-anchor="middle">⚙️</text>
      <text x="1190" y="880" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.7)" text-anchor="middle">More</text>
    </g>
    
    <!-- Toast Notification -->
    <rect x="1250" y="20" width="320" height="40" rx="12" fill="url(#buttonGradient)" filter="url(#shadow)"/>
    <text x="1410" y="44" font-family="Arial, sans-serif" font-size="14" font-weight="500" fill="white" text-anchor="middle">🎉 Item added to order!</text>
  </svg>