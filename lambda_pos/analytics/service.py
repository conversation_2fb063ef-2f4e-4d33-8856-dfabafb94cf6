from datetime import timedelta

from django.db import models
from django.db.models import Avg
from django.db.models import Count
from django.db.models import QuerySet
from django.db.models import Sum
from django.utils import timezone
from orders.models import Order
from orders.models import OrderItem
from products.models import Article


class AnalyticsService:
    """Enhanced service class for handling sales analytics"""

    def __init__(self):
        pass

    def get_popular_items_by_quantity(
        self,
        limit=10,
        status_filter=None,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """
        Get most popular sold items by quantity

        Args:
            limit (int): Number of items to return (default: 10)
            status_filter (list): Filter by order status (optional)
            date_from (str): Start date for filtering (YYYY-MM-DD)
            date_to (str): End date for filtering (YYYY-MM-DD)
            date_range_preset (str): Preset date range filter

        Returns:
            QuerySet with article info and sales data
        """
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        # Filter by status if specified
        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        # Aggregate by article
        popular_items = (
            queryset.values(
                "orderitem__article__id",
                "orderitem__article__name",
                "orderitem__article__price",
                "orderitem__article__sub_family__family__name",
            )
            .annotate(
                total_quantity_sold=Sum("orderitem__quantity"),
                total_orders=Count("orderitem__order", distinct=True),
                total_revenue=Sum("orderitem__line_total"),
                avg_quantity_per_order=models.Avg("orderitem__quantity"),
            )
            .filter(total_quantity_sold__gt=0)
            .order_by("-total_quantity_sold")[:limit]
        )

        return popular_items

    def get_popular_items_by_revenue(
        self,
        limit=10,
        status_filter=None,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """
        Get items ranked by total revenue instead of quantity
        """
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        popular_items = (
            queryset.values(
                "orderitem__article__id",
                "orderitem__article__name",
                "orderitem__article__price",
                "orderitem__article__product_type__name",
            )
            .annotate(
                total_quantity_sold=Sum("orderitem__quantity"),
                total_orders=Count("orderitem__order", distinct=True),
                total_revenue=Sum("orderitem__line_total"),
                avg_quantity_per_order=models.Avg("orderitem__quantity"),
            )
            .filter(total_revenue__gt=0)
            .order_by("-total_revenue")[:limit]
        )

        return popular_items

    def get_article_sales_stats(
        self,
        article_id,
        status_filter=None,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """
        Get sales statistics for a specific article
        """
        queryset = OrderItem.objects.filter(article_id=article_id)

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(order__status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(order__created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(order__revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(order__workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(order__serving_period=serving_period)

        stats = queryset.aggregate(
            total_quantity_sold=Sum("quantity"),
            total_orders=Count("order", distinct=True),
            total_revenue=Sum("line_total"),
            avg_quantity_per_order=models.Avg("quantity"),
        )

        # Get article details
        try:
            article = Article.objects.get(id=article_id)
            article_name = article.name
            article_price = article.price
            sub_family_name = article.sub_family.name if article.sub_family else None
        except Article.DoesNotExist:
            article_name = "Unknown"
            article_price = 0
            sub_family_name = None

        return {
            "orderitem__article__id": article_id,
            "orderitem__article__name": article_name,
            "orderitem__article__price": article_price,
            "orderitem__article__sub_family_name": sub_family_name,
            "total_quantity_sold": stats["total_quantity_sold"] or 0,
            "total_orders": stats["total_orders"] or 0,
            "total_revenue": stats["total_revenue"] or 0,
            "avg_quantity_per_order": round(stats["avg_quantity_per_order"] or 0, 2),
        }

    def get_sales_summary(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
        status_filter=None
    ):
        """
        Get overall sales summary
        """
        queryset = Order.objects.all()

        # For paid orders, filter by payment date instead of order creation date
        if status_filter and Order.STATUS.PAID in status_filter:
            # Apply date filters to payment date for paid orders
            queryset = self._apply_date_filters_to_payments(
                queryset, date_from, date_to, date_range_preset
            )
        else:
            # Apply date filters to order creation date for other statuses
            queryset = self._apply_date_filters(
                queryset, date_from, date_to, date_range_preset
            )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        summary = queryset.aggregate(
            total_orders=Count("id"),
            total_revenue=Sum("grand_total"),
            avg_order_value=models.Avg("grand_total"),
        )

        total_items_sold = (
            OrderItem.objects.filter(order__in=queryset).aggregate(
                total_items=Sum("quantity")
            )["total_items"]
            or 0
        )

        # Calculate period days from date range
        period_days = None
        if date_from and date_to:
            try:
                from datetime import datetime

                start = datetime.strptime(date_from, "%Y-%m-%d").date()
                end = datetime.strptime(date_to, "%Y-%m-%d").date()
                period_days = (end - start).days + 1
            except ValueError:
                period_days = None

        return {
            "period_days": period_days,
            "total_orders": summary["total_orders"] or 0,
            "total_revenue": summary["total_revenue"] or 0,
            "avg_order_value": round(summary["avg_order_value"] or 0, 2),
            "total_items_sold": total_items_sold,
            "generated_at": timezone.now(),
        }

    def get_sales_by_hour(
        self,
        status_filter=None,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """Get sales data grouped by hour of day"""
        queryset = Order.objects.all()

        # Apply date filters - use payment date for paid orders
        if self._should_use_payment_date(status_filter):
            queryset = self._apply_date_filters_to_payments(
                queryset, date_from, date_to, date_range_preset
            )
        else:
            queryset = self._apply_date_filters(
                queryset, date_from, date_to, date_range_preset
            )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        # Use payment hour for paid orders, order hour for others
        if self._should_use_payment_date(status_filter):
            return (
                queryset.extra(
                    select={"hour": "EXTRACT(hour FROM orders_orderpayment.created_at)"})
                .values("hour")
                .annotate(
                    total_orders=Count("id", distinct=True),
                    total_revenue=Sum("grand_total"),
                    avg_order_value=Avg("grand_total"),
                )
                .order_by("hour")
            )
        else:
            return (
                queryset.extra(
                    select={"hour": "EXTRACT(hour FROM orders_order.created_at)"})
                .values("hour")
                .annotate(
                    total_orders=Count("id"),
                    total_revenue=Sum("grand_total"),
                    avg_order_value=Avg("grand_total"),
                )
                .order_by("hour")
            )

    def get_sales_by_day_of_week(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """Get sales data grouped by day of week"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        return (
            queryset.extra(
                select={"day_of_week": "EXTRACT(dow FROM created_at)"})
            .values("day_of_week")
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
            )
            .order_by("day_of_week")
        )

    def get_order_status_analytics(
        self, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get detailed analytics by order status"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        # Get total count for percentage calculation
        total_orders = queryset.count()

        return (
            queryset.values("status")
            .annotate(
                count=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
            )
            .annotate(percentage=models.F("count") * 100.0 / total_orders)
            .order_by("-count")
        )

    def get_serving_period_analytics(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None
    ):
        """Get analytics by serving period (Morning, Lunch, etc.)"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by specific serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        return (
            queryset.values("serving_period")
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                total_items=Sum("orderitem__quantity"),
            )
            .order_by("-total_revenue")
        )

    def get_workstation_performance(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None
    ):
        """Get performance metrics by workstation"""
        queryset = Order.objects.select_related("workstation")

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by specific workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        return (
            queryset.values(
                "workstation__id", "workstation__name", "workstation__location"
            )
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                total_items=Sum("orderitem__quantity"),
            )
            .order_by("-total_revenue")
        )

    def get_tips_analytics(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None
    ):
        """Get tips analytics"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(serving_period=serving_period)

        # Get orders with tips - use proper relationship check
        orders_with_tips = queryset.filter(tips__isnull=False).distinct()

        # Calculate total orders for percentage
        total_orders = queryset.count()

        tips_summary = orders_with_tips.aggregate(
            total_tips=Sum("tips__amount"),
            avg_tips_per_order=Avg("tips__amount"),
            orders_with_tips=Count("id", distinct=True),
        )
        tips_summary["total_orders"] = total_orders

        # Tips by customer (if customer name is provided)
        tips_by_customer = (
            queryset.filter(tips__customer__isnull=False)
            .values("tips__customer")
            .annotate(
                total_tips=Sum("tips__amount"),
                order_count=Count("id", distinct=True)
            )
            .order_by("-total_tips")
        )

        return {"summary": tips_summary, "by_customer": tips_by_customer}

    def get_payment_analytics(
        self, date_from=None, date_to=None, date_range_preset=None, status_filter=None
    ):
        """Get detailed payment analytics"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Payment method analysis
        payment_methods = (
            queryset.values(
                "orderpayment__payment_method__id", "orderpayment__payment_method__name"
            )
            .annotate(
                total_payments=Count("orderpayment"),
                total_amount=Sum("orderpayment__amount"),
                avg_payment=Avg("orderpayment__amount"),
            )
            .order_by("-total_amount")
        )

        # Payment status analysis
        payment_status = queryset.aggregate(
            fully_paid=Count("id", filter=models.Q(status=Order.STATUS.PAID)),
            partially_paid=Count(
                "id", filter=models.Q(status=Order.STATUS.PARTIALLY_PAID)
            ),
            unpaid=Count(
                "id",
                filter=models.Q(
                    status__in=[
                        Order.STATUS.PRINTED,
                        Order.STATUS.SENT,
                        Order.STATUS.PREPARED,
                    ]
                ),
            ),
            voided=Count("id", filter=models.Q(status=Order.STATUS.VOIDED)),
        )

        return {"payment_methods": payment_methods, "payment_status": payment_status}

    def get_discount_analytics(
        self, date_from=None, date_to=None, date_range_preset=None, status_filter=None
    ):
        """Get discount analytics"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Calculate discount percentage separately to avoid type conflicts
        discount_stats = queryset.aggregate(
            total_discount=Sum("discount"),
            avg_discount=Avg("discount"),
            orders_with_discount=Count("id", filter=models.Q(discount__gt=0)),
            total_orders=Count("id"),
            avg_grand_total=Avg("grand_total"),
        )

        # Calculate percentage manually using Decimal arithmetic
        from decimal import Decimal

        if discount_stats["avg_grand_total"] and discount_stats["avg_grand_total"] > 0:
            discount_percentage = float(
                (discount_stats["avg_discount"] /
                 discount_stats["avg_grand_total"])
                * Decimal("100")
            )
        else:
            discount_percentage = 0.0

        discount_stats["discount_percentage"] = discount_percentage
        return discount_stats

    def get_split_order_analytics(
        self, date_from=None, date_to=None, date_range_preset=None, status_filter=None
    ):
        """Get analytics for split orders"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Original orders that were split
        split_orders = queryset.filter(
            orderitem__splited_from__isnull=False).distinct()

        # Orders created from splits
        split_children = queryset.filter(splited_from__isnull=False)

        return {
            "original_split_orders": split_orders.count(),
            "split_children": split_children.count(),
            "split_children_revenue": split_children.aggregate(
                total_revenue=Sum("grand_total")
            )["total_revenue"]
            or 0,
            "avg_split_value": split_children.aggregate(avg_value=Avg("grand_total"))[
                "avg_value"
            ]
            or 0,
        }

    def get_void_analytics(self, date_from=None, date_to=None, date_range_preset=None):
        """Get analytics for voided orders and items"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        # Voided orders
        voided_orders = queryset.filter(status=Order.STATUS.VOIDED)

        # Voided items - apply same date filters
        voided_items_queryset = OrderItem.objects.all()
        voided_items_queryset = self._apply_date_filters(
            voided_items_queryset, date_from, date_to, date_range_preset
        )
        voided_items = voided_items_queryset.filter(
            status=OrderItem.STATUS.VOIDED)

        # Void reasons
        void_reasons = (
            voided_items.values("orderitemvoid__reason__name")
            .annotate(count=Count("id"), total_value=Sum("line_total"))
            .order_by("-count")
        )

        return {
            "voided_orders": {
                "count": voided_orders.count(),
                "total_value": voided_orders.aggregate(total=Sum("grand_total"))[
                    "total"
                ]
                or 0,
            },
            "voided_items": {
                "count": voided_items.count(),
                "total_value": voided_items.aggregate(total=Sum("line_total"))["total"]
                or 0,
            },
            "void_reasons": void_reasons,
        }

    def get_comprehensive_dashboard(
        self, date_from=None, date_to=None, date_range_preset=None, status_filter=None
    ):
        """Get comprehensive dashboard data"""
        return {
            "sales_summary": self.get_sales_summary(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
            ),
            "popular_items": self.get_popular_items_by_quantity(
                limit=10,
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "top_revenue_items": self.get_popular_items_by_revenue(
                limit=10,
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "sales_by_hour": self.get_sales_by_hour(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "sales_by_day": self.get_sales_by_day_of_week(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "sales_by_family": self.get_sales_by_family(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "table_performance": self.get_table_performance(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "user_performance": self.get_user_performance(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "order_status": self.get_order_status_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
            ),
            "serving_period": self.get_serving_period_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "workstation_performance": self.get_workstation_performance(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "tips_analytics": self.get_tips_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "payment_analytics": self.get_payment_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "discount_analytics": self.get_discount_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "split_order_analytics": self.get_split_order_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
                status_filter=status_filter,
            ),
            "void_analytics": self.get_void_analytics(
                date_from=date_from,
                date_to=date_to,
                date_range_preset=date_range_preset,
            ),
        }

    def get_sales_by_family(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
        workstation=None,
        serving_period=None,
    ):
        """Get sales data grouped by product family"""
        queryset = OrderItem.objects.select_related(
            "article__sub_family__family")

        # Apply date filters - use payment date for paid orders
        if self._should_use_payment_date(status_filter):
            queryset = self._apply_date_filters_to_order_items_payments(
                queryset, date_from, date_to, date_range_preset
            )
        else:
            queryset = self._apply_date_filters(
                queryset, date_from, date_to, date_range_preset
            )

        if status_filter:
            queryset = queryset.filter(order__status__in=status_filter)

        # Filter by user if specified
        if user_id:
            queryset = queryset.filter(order__created_by_id=user_id)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(order__revenue_center_id=revenue_center)

        # Filter by workstation if specified
        if workstation:
            queryset = queryset.filter(order__workstation_id=workstation)

        # Filter by serving period if specified
        if serving_period:
            queryset = queryset.filter(order__serving_period=serving_period)

        return (
            queryset.values(
                "article__sub_family__family__id",
                "article__sub_family__family__name",
                "article__sub_family__family__code",
            )
            .annotate(
                total_quantity_sold=Sum("quantity"),
                total_orders=Count("order", distinct=True),
                total_revenue=Sum("line_total"),
                unique_articles=Count("article", distinct=True),
            )
            .order_by("-total_revenue")
        )

    def get_sales_hierarchy_report(
        self,
        status_filter=None,
        date_from=None,
        date_to=None,
        user_id=None,
        date_range_preset=None,
        include_sub_family=False,
        include_articles=False,
    ):
        """
        Get comprehensive sales report with Family → Sub-Family → Article hierarchy
        Returns structured data for cascading reports
        """
        queryset = OrderItem.objects.select_related(
            "article__sub_family__family", "article__sub_family", "article"
        )

        if user_id:
            queryset = queryset.filter(order__created_by_id=user_id)

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(order__status__in=status_filter)

        # Get all families with their totals
        families = (
            queryset.values(
                "article__sub_family__family__id",
                "article__sub_family__family__name",
                "article__sub_family__family__code",
            )
            .annotate(
                total_quantity_sold=Sum("quantity"),
                total_orders=Count("order", distinct=True),
                total_revenue=Sum("line_total"),
                unique_sub_families=Count(
                    "article__sub_family", distinct=True),
                unique_articles=Count("article", distinct=True),
            )
            .order_by("-total_revenue")
        )

        # Structure the data hierarchically
        hierarchy = {}

        for family in families:
            family_id = family["article__sub_family__family__id"]
            hierarchy[family_id] = {
                "family": {
                    "id": family_id,
                    "name": family["article__sub_family__family__name"],
                    "code": family["article__sub_family__family__code"],
                    "total_quantity_sold": family["total_quantity_sold"],
                    "total_orders": family["total_orders"],
                    "total_revenue": family["total_revenue"],
                    "unique_sub_families": family["unique_sub_families"],
                    "unique_articles": family["unique_articles"],
                },
                "sub_families": {},
                "total_quantity_sold": family["total_quantity_sold"],
                "total_orders": family["total_orders"],
                "total_revenue": family["total_revenue"],
            }

        # Include sub-families if requested
        if include_sub_family:
            # Get sub-families with their totals
            sub_families = (
                queryset.values(
                    "article__sub_family__family__id",
                    "article__sub_family__family__name",
                    "article__sub_family__family__code",
                    "article__sub_family__id",
                    "article__sub_family__name",
                    "article__sub_family__code",
                )
                .annotate(
                    total_quantity_sold=Sum("quantity"),
                    total_orders=Count("order", distinct=True),
                    total_revenue=Sum("line_total"),
                    unique_articles=Count("article", distinct=True),
                )
                .order_by("article__sub_family__family__name", "-total_revenue")
            )

            for sub_family in sub_families:
                family_id = sub_family["article__sub_family__family__id"]
                sub_family_id = sub_family["article__sub_family__id"]

                if family_id in hierarchy:
                    hierarchy[family_id]["sub_families"][sub_family_id] = {
                        "id": sub_family_id,
                        "name": sub_family["article__sub_family__name"],
                        "code": sub_family["article__sub_family__code"],
                        "total_quantity_sold": sub_family["total_quantity_sold"],
                        "total_orders": sub_family["total_orders"],
                        "total_revenue": sub_family["total_revenue"],
                        "unique_articles": sub_family["unique_articles"],
                        "articles": [],
                    }

            # Include articles if requested
            if include_articles:
                # Get articles with their totals
                articles = (
                    queryset.values(
                        "article__sub_family__family__id",
                        "article__sub_family__family__name",
                        "article__sub_family__family__code",
                        "article__sub_family__id",
                        "article__sub_family__name",
                        "article__sub_family__code",
                        "article__id",
                        "article__name",
                        "article__code",
                        "article__price",
                    )
                    .annotate(
                        total_quantity_sold=Sum("quantity"),
                        total_orders=Count("order", distinct=True),
                        total_revenue=Sum("line_total"),
                        avg_quantity_per_order=Avg("quantity"),
                    )
                    .order_by(
                        "article__sub_family__family__name",
                        "article__sub_family__name",
                        "-total_revenue",
                    )
                )

                for article in articles:
                    family_id = article["article__sub_family__family__id"]
                    sub_family_id = article["article__sub_family__id"]
                    article_id = article["article__id"]

                    if (
                        family_id in hierarchy
                        and sub_family_id in hierarchy[family_id]["sub_families"]
                    ):
                        hierarchy[family_id]["sub_families"][sub_family_id][
                            "articles"
                        ].append(
                            {
                                "id": article_id,
                                "name": article["article__name"],
                                "code": article["article__code"],
                                "price": article["article__price"],
                                "total_quantity_sold": article["total_quantity_sold"],
                                "total_orders": article["total_orders"],
                                "total_revenue": article["total_revenue"],
                                "avg_quantity_per_order": round(
                                    article["avg_quantity_per_order"] or 0, 2
                                ),
                            }
                        )

        return {
            "hierarchy": hierarchy,
            "summary": {
                "total_families": len(families),
                "total_sub_families": len(sub_families) if include_sub_family else 0,
                "total_articles": len(articles) if include_articles else 0,
                "total_quantity_sold": sum(f["total_quantity_sold"] for f in families),
                "total_revenue": sum(f["total_revenue"] for f in families),
                "total_orders": sum(f["total_orders"] for f in families),
            },
        }

    def get_table_performance(
        self, date_from=None, date_to=None, date_range_preset=None, status_filter=None
    ):
        """Get performance metrics by table"""
        queryset = Order.objects.select_related("table")

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        return (
            queryset.values("table__id", "table__name", "table__code")
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                total_items=Sum("orderitem__quantity"),
            )
            .order_by("-total_revenue")
        )

    def get_user_performance(
        self,
        date_from=None,
        date_to=None,
        date_range_preset=None,
        status_filter=None,
        user_id=None,
        revenue_center=None,
    ):
        """Get performance metrics by user/waiter"""
        queryset = Order.objects.select_related("created_by")

        if user_id:
            queryset = queryset.filter(created_by_id=user_id)

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        # Filter by revenue center if specified
        if revenue_center:
            queryset = queryset.filter(revenue_center=revenue_center)

        return (
            queryset.values(
                "created_by__id",
                "created_by__username",
                "created_by__first_name",
                "created_by__last_name",
            )
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                total_items=Sum("orderitem__quantity"),
            )
            .order_by("-total_revenue")
        )

    def get_payment_method_stats(
        self, status_filter=None, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get statistics by payment method"""
        queryset = Order.objects.select_related("payment_method")

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        return (
            queryset.values("payment_method__id", "payment_method__name")
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                percentage=Count("id") * 100.0 / Count("id").over(),
            )
            .order_by("-total_revenue")
        )

    def get_daily_sales_trend(
        self, status_filter=None, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get daily sales trend"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        return (
            queryset.extra(select={"date": "DATE(orders_order.created_at)"})
            .values("date")
            .annotate(
                total_orders=Count("id"),
                total_revenue=Sum("grand_total"),
                avg_order_value=Avg("grand_total"),
                total_items=Sum("orderitem__quantity"),
            )
            .order_by("date")
        )

    def get_order_status_breakdown(
        self, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get breakdown of orders by status"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        return (
            queryset.values("status")
            .annotate(
                count=Count("id"),
                total_revenue=Sum("grand_total"),
                percentage=Count("id") * 100.0 / Count("id").over(),
            )
            .order_by("-count")
        )

    def get_low_performing_items(
        self, limit=10, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get items with lowest sales performance"""
        # Get all articles that have been ordered
        ordered_articles_queryset = OrderItem.objects.all()
        ordered_articles_queryset = self._apply_date_filters(
            ordered_articles_queryset, date_from, date_to, date_range_preset
        )
        ordered_articles = ordered_articles_queryset.values(
            "article_id").distinct()

        # Get articles with low sales
        queryset = OrderItem.objects.all()
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        return (
            queryset.filter(article_id__in=ordered_articles)
            .values(
                "article__id",
                "article__name",
                "article__price",
                "article__sub_family__family__name",
            )
            .annotate(
                total_quantity_sold=Sum("quantity"),
                total_orders=Count("order", distinct=True),
                total_revenue=Sum("line_total"),
            )
            .order_by("total_quantity_sold", "total_revenue")[:limit]
        )

    def get_advanced_summary(
        self, status_filter=None, date_from=None, date_to=None, date_range_preset=None
    ):
        """Get comprehensive analytics summary"""
        queryset = Order.objects.all()

        # Apply date filters
        queryset = self._apply_date_filters(
            queryset, date_from, date_to, date_range_preset
        )

        if status_filter:
            queryset = queryset.filter(status__in=status_filter)

        paid_orders = queryset.filter(status=Order.STATUS.PAID)

        # Basic metrics
        summary = paid_orders.aggregate(
            total_orders=Count("id"),
            total_revenue=Sum("grand_total"),
            avg_order_value=Avg("grand_total"),
            total_discount=Sum("discount"),
        )

        # Additional metrics
        total_items_sold = (
            OrderItem.objects.filter(order__in=paid_orders).aggregate(
                total_items=Sum("quantity")
            )["total_items"]
            or 0
        )

        # Unique customers (assuming tables represent customers)
        unique_tables = paid_orders.values("table").distinct().count()

        # Calculate period days from date range
        period_days = 30  # Default
        if date_from and date_to:
            try:
                from datetime import datetime

                start = datetime.strptime(date_from, "%Y-%m-%d").date()
                end = datetime.strptime(date_to, "%Y-%m-%d").date()
                period_days = (end - start).days + 1
            except ValueError:
                period_days = 30

        # Orders per day
        orders_per_day = (
            summary["total_orders"] / max(period_days, 1) if period_days else 0
        )

        # Revenue per day
        revenue_per_day = (
            summary["total_revenue"] / max(period_days, 1)
            if period_days and summary["total_revenue"]
            else 0
        )

        return {
            "period_days": period_days,
            "total_orders": summary["total_orders"] or 0,
            "total_revenue": summary["total_revenue"] or 0,
            "avg_order_value": round(summary["avg_order_value"] or 0, 2),
            "total_items_sold": total_items_sold,
            "total_discount": summary["total_discount"] or 0,
            "unique_tables": unique_tables,
            "orders_per_day": round(orders_per_day, 2),
            "revenue_per_day": round(revenue_per_day, 2),
            "items_per_order": round(
                total_items_sold / max(summary["total_orders"], 1), 2
            ),
            "generated_at": timezone.now(),
        }

    def _apply_date_filters(
        self, queryset: QuerySet, date_from=None, date_to=None, date_range_preset=None
    ):
        """Helper method to apply date filters using core filters logic"""
        from core.filters import DATE_RANGE_PRESET
        from datetime import date, timedelta

        # Apply preset date range filters
        if date_range_preset:
            today = date.today()

            # Calculate start and end of this week (Monday to Sunday)
            start_of_this_week = today - timedelta(days=today.weekday())
            end_of_this_week = start_of_this_week + timedelta(days=6)
            # Calculate start and end of last week
            start_of_last_week = start_of_this_week - timedelta(days=7)
            end_of_last_week = start_of_this_week - timedelta(days=1)

            preset_filters = {
                DATE_RANGE_PRESET.TODAY: lambda: queryset.filter(
                    created_at__date=today
                ),
                DATE_RANGE_PRESET.YESTERDAY: lambda: queryset.filter(
                    created_at__date=today - timedelta(days=1)
                ),
                DATE_RANGE_PRESET.THIS_WEEK: lambda: queryset.filter(
                    created_at__date__gte=start_of_this_week,
                    created_at__date__lte=end_of_this_week,
                ),
                DATE_RANGE_PRESET.LAST_WEEK: lambda: queryset.filter(
                    created_at__date__gte=start_of_last_week,
                    created_at__date__lte=end_of_last_week,
                ),
                DATE_RANGE_PRESET.LAST_7_DAYS: lambda: queryset.filter(
                    created_at__date__gte=today - timedelta(days=7),
                    created_at__date__lte=today,
                ),
                DATE_RANGE_PRESET.LAST_30_DAYS: lambda: queryset.filter(
                    created_at__date__gte=today - timedelta(days=30),
                    created_at__date__lte=today,
                ),
                DATE_RANGE_PRESET.LAST_90_DAYS: lambda: queryset.filter(
                    created_at__date__gte=today - timedelta(days=90),
                    created_at__date__lte=today,
                ),
                DATE_RANGE_PRESET.THIS_MONTH: lambda: queryset.filter(
                    created_at__date__gte=today.replace(day=1),
                    created_at__date__lte=today,
                ),
                DATE_RANGE_PRESET.LAST_MONTH: lambda: self._get_last_month_filter(
                    queryset, today
                ),
                DATE_RANGE_PRESET.FIRST_QUARTER: lambda: queryset.filter(
                    created_at__quarter=1, created_at__year=today.year
                ),
                DATE_RANGE_PRESET.SECOND_QUARTER: lambda: queryset.filter(
                    created_at__quarter=2, created_at__year=today.year
                ),
                DATE_RANGE_PRESET.THIRD_QUARTER: lambda: queryset.filter(
                    created_at__quarter=3, created_at__year=today.year
                ),
                DATE_RANGE_PRESET.FOURTH_QUARTER: lambda: queryset.filter(
                    created_at__quarter=4, created_at__year=today.year
                ),
                DATE_RANGE_PRESET.THIS_YEAR: lambda: queryset.filter(
                    created_at__date__gte=today.replace(month=1, day=1),
                    created_at__date__lte=today,
                ),
            }

            filter_func = preset_filters.get(date_range_preset)
            if filter_func:
                queryset = filter_func()

        # Apply custom date range filters
        if date_from and date_to:
            queryset = queryset.filter(
                created_at__date__gte=date_from, created_at__date__lte=date_to
            )
        elif date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        elif date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset

    def _apply_date_filters_to_payments(self, queryset, date_from, date_to, date_range_preset):
        """Apply date filters to payment dates instead of order creation dates"""
        from datetime import date, timedelta
        from core.filters import DATE_RANGE_PRESET

        # Apply custom date range filters to payment dates
        if date_from and date_to:
            return queryset.filter(
                orderpayment__created_at__date__gte=date_from,
                orderpayment__created_at__date__lte=date_to
            ).distinct()
        elif date_from:
            return queryset.filter(orderpayment__created_at__date__gte=date_from).distinct()
        elif date_to:
            return queryset.filter(orderpayment__created_at__date__lte=date_to).distinct()

        # Apply preset date range filters to payment dates
        if date_range_preset:
            today = date.today()

            # Calculate start and end of this week (Monday to Sunday)
            start_of_this_week = today - timedelta(days=today.weekday())
            end_of_this_week = start_of_this_week + timedelta(days=6)
            # Calculate start and end of last week
            start_of_last_week = start_of_this_week - timedelta(days=7)
            end_of_last_week = start_of_this_week - timedelta(days=1)

            preset_filters = {
                DATE_RANGE_PRESET.TODAY: lambda: queryset.filter(
                    orderpayment__created_at__date=today
                ).distinct(),
                DATE_RANGE_PRESET.YESTERDAY: lambda: queryset.filter(
                    orderpayment__created_at__date=today - timedelta(days=1)
                ).distinct(),
                DATE_RANGE_PRESET.THIS_WEEK: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=start_of_this_week,
                    orderpayment__created_at__date__lte=end_of_this_week,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_WEEK: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=start_of_last_week,
                    orderpayment__created_at__date__lte=end_of_last_week,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_7_DAYS: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=today -
                    timedelta(days=7),
                    orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_30_DAYS: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=today -
                    timedelta(days=30),
                    orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_90_DAYS: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=today -
                    timedelta(days=90),
                    orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.THIS_MONTH: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=today.replace(day=1),
                    orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_MONTH: lambda: self._get_last_month_payment_filter(
                    queryset, today
                ),
                DATE_RANGE_PRESET.THIS_YEAR: lambda: queryset.filter(
                    orderpayment__created_at__date__gte=today.replace(
                        month=1, day=1),
                    orderpayment__created_at__date__lte=today,
                ).distinct(),
            }

            filter_func = preset_filters.get(date_range_preset)
            if filter_func:
                return filter_func()

        return queryset

    def _get_last_month_payment_filter(self, queryset, today):
        """Get last month filter for payment dates"""
        from calendar import monthrange

        if today.month == 1:
            last_month = 12
            last_month_year = today.year - 1
        else:
            last_month = today.month - 1
            last_month_year = today.year

        last_month_start = today.replace(
            year=last_month_year, month=last_month, day=1
        )
        last_month_end = today.replace(
            year=last_month_year,
            month=last_month,
            day=monthrange(last_month_year, last_month)[1],
        )

        return queryset.filter(
            orderpayment__created_at__date__gte=last_month_start,
            orderpayment__created_at__date__lte=last_month_end,
        ).distinct()

    def _should_use_payment_date(self, status_filter):
        """Determine if we should filter by payment date instead of order creation date"""
        return status_filter and Order.STATUS.PAID in status_filter

    def _apply_date_filters_to_order_items_payments(self, queryset, date_from, date_to, date_range_preset):
        """Apply date filters to OrderItems based on payment dates"""
        from datetime import date, timedelta
        from core.filters import DATE_RANGE_PRESET

        # Apply custom date range filters to payment dates
        if date_from and date_to:
            return queryset.filter(
                order__orderpayment__created_at__date__gte=date_from,
                order__orderpayment__created_at__date__lte=date_to
            ).distinct()
        elif date_from:
            return queryset.filter(order__orderpayment__created_at__date__gte=date_from).distinct()
        elif date_to:
            return queryset.filter(order__orderpayment__created_at__date__lte=date_to).distinct()

        # Apply preset date range filters to payment dates
        if date_range_preset:
            today = date.today()

            # Calculate start and end of this week (Monday to Sunday)
            start_of_this_week = today - timedelta(days=today.weekday())
            end_of_this_week = start_of_this_week + timedelta(days=6)
            # Calculate start and end of last week
            start_of_last_week = start_of_this_week - timedelta(days=7)
            end_of_last_week = start_of_this_week - timedelta(days=1)

            preset_filters = {
                DATE_RANGE_PRESET.TODAY: lambda: queryset.filter(
                    order__orderpayment__created_at__date=today
                ).distinct(),
                DATE_RANGE_PRESET.YESTERDAY: lambda: queryset.filter(
                    order__orderpayment__created_at__date=today -
                    timedelta(days=1)
                ).distinct(),
                DATE_RANGE_PRESET.THIS_WEEK: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=start_of_this_week,
                    order__orderpayment__created_at__date__lte=end_of_this_week,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_WEEK: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=start_of_last_week,
                    order__orderpayment__created_at__date__lte=end_of_last_week,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_7_DAYS: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=today -
                    timedelta(days=7),
                    order__orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_30_DAYS: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=today -
                    timedelta(days=30),
                    order__orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_90_DAYS: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=today -
                    timedelta(days=90),
                    order__orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.THIS_MONTH: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=today.replace(
                        day=1),
                    order__orderpayment__created_at__date__lte=today,
                ).distinct(),
                DATE_RANGE_PRESET.LAST_MONTH: lambda: self._get_last_month_order_items_payment_filter(
                    queryset, today
                ),
                DATE_RANGE_PRESET.THIS_YEAR: lambda: queryset.filter(
                    order__orderpayment__created_at__date__gte=today.replace(
                        month=1, day=1),
                    order__orderpayment__created_at__date__lte=today,
                ).distinct(),
            }

            filter_func = preset_filters.get(date_range_preset)
            if filter_func:
                return filter_func()

        return queryset

    def _get_last_month_order_items_payment_filter(self, queryset, today):
        """Get last month filter for OrderItems payment dates"""
        from calendar import monthrange

        if today.month == 1:
            last_month = 12
            last_month_year = today.year - 1
        else:
            last_month = today.month - 1
            last_month_year = today.year

        last_month_start = today.replace(
            year=last_month_year, month=last_month, day=1
        )
        last_month_end = today.replace(
            year=last_month_year,
            month=last_month,
            day=monthrange(last_month_year, last_month)[1],
        )

        return queryset.filter(
            order__orderpayment__created_at__date__gte=last_month_start,
            order__orderpayment__created_at__date__lte=last_month_end,
        ).distinct()

    def _get_last_month_filter(self, queryset: QuerySet, today):
        """Helper method for last month filter logic"""
        first_day_this_month = today.replace(day=1)
        last_month_end = first_day_this_month - timedelta(days=1)
        last_month_start = last_month_end.replace(day=1)
        return queryset.filter(
            created_at__date__gte=last_month_start, created_at__date__lte=last_month_end
        )
