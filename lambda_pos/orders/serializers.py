from accounts.serializers import UserSerializer
from core.dependency_injection import service_locator
from core.serializers import BaseToRepresentation
from core.serializers import CreatedByMixin
from django.db import transaction
from literals.models import VoidReason
from rest_framework import serializers
from tables.serializers import TableSerializer

from .models import Order
from .models import OrderItem
from .models import OrderItemVoid
from .models import OrderMessage
from .models import OrderPayment
from .models import Table
from .models import Tips
from .models import Workstation

# from printing.tasks import print_order_async


class OrderMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderMessage
        exclude = ["order", "is_deleted"]
        read_only_fields = ["id", "created_by"]


class OrderItemVoidSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderItemVoid
        exclude = ["order_item", "is_deleted"]


class TipsSerializer(CreatedByMixin, serializers.ModelSerializer):
    created_by = UserSerializer()

    class Meta:
        model = Tips
        exclude = ["is_deleted"]
        read_only_fields = ["id", "created_by", "order"]


class OrderItemSerializer(
    CreatedByMixin, BaseToRepresentation, serializers.ModelSerializer
):
    table_name = serializers.SerializerMethodField()
    table_code = serializers.SerializerMethodField()
    voided_reason = OrderItemVoidSerializer(
        source="orderitemvoid_set.first", required=False, read_only=True
    )

    class Meta:
        model = OrderItem
        exclude = ["is_deleted", "order"]
        read_only_fields = ["id", "created_by", "voided_reason"]

    def get_table_name(self, obj: OrderItem):

        return obj.order.table.name

    def get_table_code(self, obj: OrderItem):
        return obj.order.table.code


class BaseOrderSerializer(CreatedByMixin, serializers.ModelSerializer):
    class Meta:
        model = Order
        exclude = ["is_deleted"]
        read_only_fields = ["id", "created_by"]


class OrderMixin(serializers.Serializer):

    order_items = serializers.SerializerMethodField()
    is_empty = serializers.SerializerMethodField()

    def get_order_items(self, obj: Order):
        include_voided = self.context.get("request", {}).query_params.get(
            "include_voided", "true"
        ).lower() in ["true", "1", "yes"]

        items = (
            obj.orderitem_set.order_by("-created_at")
            if include_voided
            else obj.active_items().order_by("-created_at")
        )

        return OrderItemSerializer(items, many=True, context=self.context).data

    def get_is_empty(self, obj):
        return not self.get_order_items(obj)


class OrderCreateSerializer(BaseOrderSerializer):
    table = TableSerializer(required=False)
    order_items = OrderItemSerializer(source="orderitem_set", many=True)
    order_message = OrderMessageSerializer(
        source="ordermessage_set", many=True, required=False
    )

    def create(self, validated_data):
        request = self.context["request"]
        created_by = request.user
        order_items_data = validated_data.pop("orderitem_set", [])
        order_messages = validated_data.pop("ordermessage_set", [])
        revenue_center = validated_data.get("revenue_center")

        table_data = validated_data.pop("table", None)
        table_data["revenue_center"] = revenue_center
        table_data["created_by"] = created_by

        table = Table.objects.create(**table_data)
        validated_data["table"] = table
        validated_data["workstation"] = self.get_workstation(request)

        order: Order = super().create(validated_data)

        for item_data in order_items_data:

            OrderItem.objects.create(order=order, created_by=created_by, **item_data)

        grand_total = order.get_total_items()
        Order.objects.filter(id=order.id).update(grand_total=grand_total)

        if order_messages:
            for order_message in order_messages:

                OrderMessage.objects.create(
                    order=order, created_by=created_by, **order_message
                )

        service_locator.thermal_print_service.print_kot(order)

        return order

    def get_workstation(self, request):
        ip_address = request.META.get("HTTP_X_FORWARDED_FOR") or request.META.get(
            "REMOTE_ADDR"
        )
        return Workstation.objects.filter(ip_address=ip_address).first()

    def update(self, instance: Order, validated_data):
        payment_id = self.initial_data.get("payment_id", None)
        order_messages = validated_data.pop("ordermessage_set", [])
        order_items_data = validated_data.pop("orderitem_set", [])
        table = validated_data.pop("table", None)
        user = self.context["request"].user

        if payment_id:
            return self.handle_pay_zero(instance)

        table_code = instance.table.code

        table = Table.objects.update_or_create(code=table_code, defaults=table)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        instance.orderitem_set.all().delete()
        for item_data in order_items_data:

            OrderItem.objects.create(order=instance, created_by=user, **item_data)

        if order_messages:
            instance.orderitem_set.all().delete()
            for order_message in order_messages:
                self.context["request"].user
                OrderMessage.objects.create(
                    order=instance, created_by=user, **order_message
                )

        service_locator.thermal_print_service.print_kot(instance)
        return instance

    def handle_pay_zero(self, obj: Order):
        payment_id = self.initial_data.get("payment_id")
        OrderPayment.objects.create(
            order=obj, created_by=obj.created_by, amount=0, payment_method_id=payment_id
        )
        return obj


class OrderListSerializer(OrderMixin, BaseOrderSerializer):
    # order_items =OrderItemSerializer(source='orderitem_set',many=True)
    # is_empty = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    table = TableSerializer(required=False)


class OrderDetailSerializer(OrderMixin, BaseOrderSerializer):
    vat_breakdown = serializers.SerializerMethodField()
    vat_summary = serializers.SerializerMethodField()
    detailed_vat_breakdown = serializers.SerializerMethodField()
    table = TableSerializer(required=False)
    payment_method = serializers.SerializerMethodField()
    created_by = UserSerializer(read_only=True)
    tips = TipsSerializer(source="tips_set", many=True)
    order_message = OrderItemSerializer(source="ordermessage_set", many=True)

    def get_payment_method(self, obj: Order):
        return OrderPaymentSerializer(obj.orderpayment_set.all(), many=True).data

    def get_vat_breakdown(self, obj: Order):
        return service_locator.order_service.get_vat_breakdown(obj)

    def get_vat_summary(self, obj: Order):
        return service_locator.order_service.get_vat_summary(obj)

    def get_detailed_vat_breakdown(self, obj: Order):
        return service_locator.order_service.get_detailed_vat_breakdown(obj)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["order_items"] = self.get_order_items(instance)
        return data


# class OrderSerializer(BaseOrderSerializer):
# order_items =OrderItemSerializer(source='orderitem_set',many=True)
# vat_breakdown = serializers.SerializerMethodField()
# vat_summary = serializers.SerializerMethodField()
# detailed_vat_breakdown = serializers.SerializerMethodField()
# table = TableSerializer(required=False)
# payment_method_name = serializers.StringRelatedField(source='payment_method')
# created_by =  UserSerializer(read_only=True)
# is_empty = serializers.SerializerMethodField()


# class Meta:
#     model = Order
#     exclude = ['is_deleted']
#     read_only_fields = ['id','created_by']


class SplitItemSerializer(serializers.Serializer):
    order_item_id = serializers.UUIDField(help_text="ID of the order item to split")
    split_quantity = serializers.IntegerField(
        min_value=1, help_text="Quantity to split to new table"
    )


class SplitQuantitySerializer(serializers.Serializer):
    data = SplitItemSerializer(many=True, help_text="List of items to split")

    def validate_data(self, value):
        for split in value:
            try:
                order_item = OrderItem.objects.get(id=split["order_item_id"])

                # Check if order item can be modified
                if order_item.order.status in [Order.STATUS.PAID]:
                    raise serializers.ValidationError(
                        f"Cannot split items from paid order {order_item.order.id}"
                    )

                # Validate split quantity - allow moving full quantity
                if split["split_quantity"] > order_item.quantity:
                    raise serializers.ValidationError(
                        f"Split quantity cannot be more than current quantity for order item {split['order_item_id']}"
                    )

            except OrderItem.DoesNotExist:
                raise serializers.ValidationError(
                    f"Order item with id {split['order_item_id']} does not exist"
                )
        return value

    @transaction.atomic
    def save(self):
        user = self.context["request"].user
        split_items_data = self.validated_data["data"]

        # All items are moved to a single new check.
        # This assumes all items are from the same original order.
        first_item = OrderItem.objects.select_related("order", "order__table").get(
            id=split_items_data[0]["order_item_id"]
        )
        original_order = first_item.order

        # Create a new table for the new order
        counter = 1
        base_name = original_order.table.name
        while Table.objects.filter(name=f"{base_name}-S{counter}").exists():
            counter += 1

        new_table = Table.objects.create(
            name=f"{base_name}-S{counter}",
            seats=original_order.table.seats,
            created_by=user,
        )

        # Create the new order
        new_order = Order.objects.create(
            table=new_table,
            status=original_order.status,
            created_by=user,
            splited_from=original_order,
        )

        results = []
        for split in split_items_data:
            order_item = OrderItem.objects.get(id=split["order_item_id"])
            split_qty = split["split_quantity"]

            unit_price = (
                (order_item.line_total / order_item.quantity)
                if order_item.quantity > 0
                else 0
            )

            # Create new order item for the new order
            new_order_item = OrderItem.objects.create(
                order=new_order,
                article=order_item.article,
                quantity=split_qty,
                price=order_item.price,
                line_total=unit_price * split_qty,
                created_by=user,
                splited_from=order_item,
            )

            # Update or delete the original order item
            if split_qty == order_item.quantity:
                order_item.delete()
            else:
                order_item.quantity -= split_qty
                order_item.line_total = unit_price * order_item.quantity
                order_item.save()

            results.append(
                {
                    "new_table_name": new_table.name,
                    "split_quantity": split_qty,
                    "original_order_item_id": str(order_item.id)
                    if order_item.pk
                    else None,
                    "new_order_item_id": str(new_order_item.id),
                    "new_order_id": str(new_order.id),
                }
            )

        # If original order is now empty, delete it and its table
        if not original_order.orderitem_set.exists():
            original_table = original_order.table
            original_order.delete()
            if not Order.objects.filter(table=original_table).exists():
                original_table.delete()

        return results


class VoidOrderItemSerializer(serializers.Serializer):
    order_item_id = serializers.PrimaryKeyRelatedField(queryset=OrderItem.objects.all())
    reason = serializers.PrimaryKeyRelatedField(queryset=VoidReason.objects.all())
    original_quantity = serializers.IntegerField()
    voided_quantity = serializers.IntegerField()

    def validate(self, data):
        if data["voided_quantity"] > data["original_quantity"]:
            raise serializers.ValidationError(
                {"voided_quantity": "Voided quantity cannot exceed original quantity"}
            )

        if data["voided_quantity"] <= 0:
            raise serializers.ValidationError(
                {"voided_quantity": "Voided quantity must be greater than 0"}
            )

        return data

    @transaction.atomic
    def create(self, validated_data: dict):
        user = self.context["request"].user
        order_item: OrderItem = validated_data["order_item_id"]
        original_quantity = validated_data["original_quantity"]
        voided_quantity = validated_data["voided_quantity"]

        # Calculate remaining quantity
        remaining_quantity = original_quantity - voided_quantity

        if remaining_quantity > 0:
            # Partial void - update quantity but don't void the item
            order_item.quantity = remaining_quantity
        else:
            # Complete void - mark item as voided
            order_item.status = OrderItem.STATUS.VOIDED

        order_item.save()

        # Create void record regardless of partial or complete void
        OrderItemVoid.objects.create(
            order_item=order_item,
            voided_by=user,
            reason=validated_data["reason"],
            original_quantity=original_quantity,
            voided_quantity=voided_quantity,
        )

        return order_item


class VoidOrderItemReasonSerializer(serializers.Serializer):
    data = VoidOrderItemSerializer(many=True)

    @transaction.atomic
    def create(self, validated_data):
        results = []

        # Process all void requests
        for item_data in validated_data["data"]:
            item_data["order_item_id"] = str(item_data["order_item_id"].id)
            item_data["reason"] = str(item_data["reason"].id)
            serializer = VoidOrderItemSerializer(data=item_data, context=self.context)
            serializer.is_valid(raise_exception=True)

            order_item = serializer.save()
            results.append(order_item)

        return results

    def to_representation(self, instance):
        return {"success": "items voided"}


class OrderPaymentSerializer(CreatedByMixin, serializers.ModelSerializer):
    class Meta:
        model = OrderPayment
        exclude = ["is_deleted"]
        read_only_fields = ["id", "created_by", "order"]


class OrderPaymentCreateSerializer(CreatedByMixin, serializers.Serializer):
    payments = OrderPaymentSerializer(many=True)
    remaining_balance = serializers.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    tips = TipsSerializer(many=False, required=False, allow_null=True)

    @transaction.atomic
    def create(self, validated_data):
        order: Order = self.context["order"]
        payments_data = validated_data.get("payments")
        tips = validated_data.get("tips", None)
        created_by = self.context["request"].user
        remaining_balance = self.validated_data.get("remaining_balance", 0)

        Order.objects.filter(id=order.id).update(remaining_balance=remaining_balance)
        order.refresh_from_db()

        created_payments = []

        for payment_data in payments_data:
            payment = OrderPayment.objects.create(
                order=order,
                payment_method=payment_data["payment_method"],
                amount=payment_data["amount"],
                created_by=created_by,
            )
            created_payments.append(payment)

        if tips:

            Tips.objects.create(order=order, created_by=created_by, **tips)

        service_locator.thermal_print_service.print_order_receipt(order)
        return {
            "order": order,
            "payments": created_payments,
            "total_payment_amount": order.grand_total,
            "remaining_balance": order.remaining_balance,
            "payment_status": order.status,
            "tips": Tips.objects.filter(order=order).values(),
        }


class OrderPaymentSummarySerializer(CreatedByMixin, serializers.ModelSerializer):
    payments = OrderPaymentSerializer(
        source="orderpayment_set", many=True, read_only=True
    )
    total_paid_amount = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    remaining_balance = serializers.DecimalField(
        max_digits=10, decimal_places=2, read_only=True
    )
    tips = TipsSerializer(source="tips_set", many=True)

    class Meta:
        model = Order
        exclude = ["is_deleted"]
        read_only_fields = ["id", "created_by"]
