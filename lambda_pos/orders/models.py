from decimal import Decimal

from accounts.models import User
from core.models import BaseModel
from core.utils.exception_handler import secure_random_with_N_digits
from django.db import models
from django.db.models import F
from django.db.models import QuerySet
from django.db.models import Sum
from general.models import AppSetting
from literals.models import RevenueCenter
from literals.models import VoidReason
from literals.models import Workstation
from payment_method.models import PaymentMethod
from products.models import Article
from tables.models import Table


class Order(BaseModel):
    orderitem_set = models.QuerySet["OrderItem"]
    orderpayment_set = models.QuerySet["OrderPayment"]
    tips_set = models.QuerySet("Tips")
    ordermessage_set = models.QuerySet("OrderMessage")

    class STATUS:
        PRINTED = "Printed"
        SENT = "Sent"
        PREPARED = "Prepared"
        PAID = "Paid"
        VOIDED = "Voided"
        PARTIALLY_PAID = "Partially Paid"

        CHOICES = [
            (PRINTED, "Printed"),
            (SENT, "Sent"),
            (PREPARED, "Prepared"),
            (PAID, "Paid"),
            (VOIDED, "Voided"),
            (PARTIALLY_PAID, "Partially Paid"),
        ]
        ALL = [PRINTED, SENT, PREPARED, PAID, VOIDED, PARTIALLY_PAID]

    class SERVING_PERIOD:
        MORNING = "Morning"
        LUNCH = "Lunch"
        AFTERNOON = "Afternoon"
        EVENING = "Evening"
        SUPPER = "Supper"
        NIGHT = "Night"

        CHOICES = [
            (MORNING, "Morning"),
            (LUNCH, "Lunch"),
            (AFTERNOON, "Afternoon"),
            (EVENING, "Evening"),
            (SUPPER, "Supper"),
            (NIGHT, "Night"),
        ]

        ALL = [MORNING, LUNCH, AFTERNOON, EVENING, SUPPER, NIGHT]

    status = models.CharField(
        max_length=20, choices=STATUS.CHOICES, default=STATUS.PRINTED
    )
    table = models.ForeignKey(Table, on_delete=models.CASCADE)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    grand_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    splited_from = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True
    )
    serving_period = models.CharField(
        max_length=100, choices=SERVING_PERIOD.CHOICES, null=True, blank=True
    )
    workstation = models.ForeignKey(
        Workstation, on_delete=models.CASCADE, null=True, blank=True
    )
    remaining_balance = models.DecimalField(decimal_places=2, max_digits=10, default=0)
    revenue_center = models.ForeignKey(
        RevenueCenter, on_delete=models.CASCADE, null=True
    )
    code = models.CharField(max_length=20, null=True)

    def __str__(self):
        return f"Order {self.id} - Table {self.table.name}"

    def save(self, *args, **kwargs):
        if not self.code:
            self.code = secure_random_with_N_digits(6)

        super().save(*args, **kwargs)

        if self.pk and self.status == Order.STATUS.VOIDED:
            Table.objects.filter(id=self.table.id).update(status=Table.STATUS.VOIDED)

    def get_total_items(self) -> Decimal:
        return (
            OrderItem.objects.filter(order=self).aggregate(
                total=Sum(F("quantity") * F("price"))
            )["total"]
            or 0
        )

    @property
    def total_tips(self):
        return self.tips_set.aggregate(total=Sum("amount"))["total"] or 0

    @property
    def is_kot(self):
        if self.splited_from:
            return False

        return self.status in (
            self.STATUS.SENT,
            self.STATUS.PRINTED,
            self.STATUS.VOIDED,
        )

    def active_items(self) -> QuerySet["OrderItem"]:
        return self.orderitem_set.exclude(status=OrderItem.STATUS.VOIDED)

    @property
    def total_paid_amount(self):
        return self.orderpayment_set.aggregate(total=Sum("amount"))["total"] or 0

    @property
    def is_fully_paid(self):

        return self.grand_total == self.total_paid_amount

    @property
    def is_partially_paid(self):
        return 0 < self.total_paid_amount < self.grand_total

    def update_payment_status(self):
        if self.is_fully_paid:
            self.status = self.STATUS.PAID
        elif self.is_partially_paid:
            self.status = self.STATUS.PARTIALLY_PAID
        self.save()


class OrderPayment(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"Payment {self.amount} via {self.payment_method.name} for Order {self.order.id}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if self.amount == 0:
            return
        self.order.update_payment_status()


class Tips(BaseModel):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    customer = models.CharField(max_length=200, null=True, blank=True)

    class Meta:
        verbose_name_plural = "Tips"

    def __str__(self):
        return f"Tip {self.amount} for Order {self.order.id}"


class OrderItem(BaseModel):
    orderitemvoid_set = models.QuerySet["OrderItemVoid"]

    class STATUS:
        DONE = "Done"
        PENDING = "Pending"
        CANCELLED = "Cancelled"
        VOIDED = "Voided"

        CHOICES = [
            (DONE, "Done"),
            (PENDING, "Pending"),
            (CANCELLED, "Cancelled"),
            (VOIDED, "Voided"),
        ]
        ALL = [DONE, PENDING, CANCELLED, VOIDED]

    orderitemvat_set = models.QuerySet["OrderItemVat"]
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    article = models.ForeignKey(Article, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(
        max_length=10, choices=STATUS.CHOICES, default=STATUS.PENDING
    )
    splited_from = models.ForeignKey(
        "self", on_delete=models.CASCADE, null=True, blank=True
    )

    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    total_vat_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    line_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True)

    def __str__(self):
        return f"{self.quantity} x {self.article.name} (Order {self.order.id})"

    @property
    def is_kot(self):
        if self.splited_from:
            return False

        return self.status in (self.STATUS.VOIDED)


class OrderItemVoid(BaseModel):
    order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE)
    voided_by = models.ForeignKey(User, on_delete=models.CASCADE)
    original_quantity = models.PositiveIntegerField(null=True, blank=True)
    voided_quantity = models.PositiveIntegerField(null=True, blank=True)
    reason = models.ForeignKey(
        VoidReason, on_delete=models.CASCADE, null=True, blank=True
    )

    def __str__(self):
        return (
            f"Voided {self.order_item.article.name} (Order {self.order_item.order.id})"
        )


class OrderItemVat(BaseModel):
    order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE)
    vat_rate = models.DecimalField(max_digits=5, decimal_places=2)
    vat_name = models.CharField(max_length=100)
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        unique_together = ["order_item", "vat_rate"]


class OrderMessage(BaseModel):
    class DESTINATION:
        KITCHEN = AppSetting.MODE.RESTAURANT
        BAR = AppSetting.MODE.BAR

        CHOICES = [
            (KITCHEN, "Kitchen"),
            (BAR, "Bar"),
        ]

    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    destination = models.CharField(max_length=20, choices=DESTINATION.CHOICES)
    message = models.TextField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
