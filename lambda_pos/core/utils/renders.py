from core.utils.format_responses import format_response_data
from djangorestframework_camel_case.render import CamelCaseJSONRenderer
from rest_framework.response import Response
import hashlib


class CustomJsonRender(CamelCaseJSONRenderer):
    def render(self, data, accepted_media_type=None, renderer_context=None):
        status_code = None
        if renderer_context:
            response: Response = renderer_context["response"]
            status_code = response.status_code

        data = format_response_data(data, status_code)
        return super().render(data, accepted_media_type, renderer_context)



# Alternative approach with predefined palette similar to your image
def get_dynamic_color_palette(identifier):
    """Generate colors from a predefined palette similar to the POS interface."""
    # Colors inspired by your image - muted, professional tones
    color_palette = [
        '#4A90E2',  # Blue (similar to Main Course)
        '#E85D75',  # Coral/Red (similar to Starters)
        '#7ED321',  # Green (similar to Pizzas)
        '#F5A623',  # Orange (similar to <PERSON><PERSON>)
        '#BD10E0',  # Purple
        '#50E3C2',  # Teal (similar to <PERSON><PERSON>)
        '#B8E986',  # Light Green
        '#9013FE',  # Deep Purple
        '#FF6900',  # Orange Red
        '#FCB900',  # Yellow (similar to Soups)
        '#00D084',  # Emerald
        '#8ED1FC',  # Light Blue
        '#ABB8C3',  # Gray Blue
        '#EB144C',  # Red
        '#9900EF',  # Purple
        '#00ADB5'   # Teal
    ]
    
    # Use hash to select color from palette
    hash_object = hashlib.md5(str(identifier).encode())
    hash_int = int(hash_object.hexdigest()[:8], 16)
    
    return color_palette[hash_int % len(color_palette)]
