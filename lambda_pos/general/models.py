from accounts.models import User
from core.models import BaseModel
from django.db import models


class AppSetting(BaseModel):
    class MODE:
        RESTAURANT = "restaurant"
        BAR = "bar"
        CAFE = "cafe"
        RETAIL = "retail"

        CHOICES = [
            (RESTAURANT, "Restaurant"),
            (BAR, "Bar"),
            (CAFE, "Cafe"),
            (RET<PERSON>IL, "Retail"),
        ]

        ALL = [RESTAURANT, BAR, CAFE, RETAIL]

    mode = models.CharField(
        max_length=20, choices=MODE.CHOICES, default=MODE.RESTAURANT
    )
    company_address = models.TextField(null=True, blank=True)
    company_phone = models.CharField(max_length=200, null=True, blank=True)
    company_email = models.EmailField(null=True, blank=True)
    default_currency = models.CharField(max_length=10, null=True, blank=True)
    company_logo = models.ImageField(null=True, blank=True)
    background_image = models.ImageField(null=True, blank=True)
    company_name = models.Char<PERSON>ield(max_length=200, null=True, blank=True)
    company_website = models.URLField(null=True, blank=True)
    order_delay = models.IntegerField(default=30)
    number_of_orders_per_page = models.IntegerField(default=15)
    track_stock = models.BooleanField(default=False)
    dark_mode = models.BooleanField(default=False)


class UserSettings(BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    language = models.CharField(max_length=10, null=True, blank=True)
    theme = models.CharField(max_length=10, null=True, blank=True)
    timezone = models.CharField(
        max_length=10, null=True, blank=True, default="UTC")
    data = models.JSONField(null=True, blank=True)
