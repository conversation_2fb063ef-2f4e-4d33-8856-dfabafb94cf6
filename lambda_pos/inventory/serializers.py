from rest_framework import serializers
from products.serializers import ArticleSerializer, FamilySerializer
from products.models import Article, SubFamily
from accounts.serializers import UserSerializer
from .models import InventoryItem, InventoryBatch, StockMovement, Supplier, PurchaseOrder, PurchaseOrderItem


class InventoryBatchSerializer(serializers.ModelSerializer):
    is_expired = serializers.ReadOnlyField()
    days_until_expiry = serializers.ReadOnlyField()
    is_expiring_soon = serializers.ReadOnlyField()
    total_value = serializers.ReadOnlyField()

    class Meta:
        model = InventoryBatch
        fields = [
            'id', 'batch_number', 'initial_quantity', 'current_quantity',
            'unit_cost', 'received_date', 'expiry_date', 'purchase_order_reference',
            'supplier_reference', 'is_expired', 'days_until_expiry',
            'is_expiring_soon', 'total_value', 'created_at', 'updated_at'
        ]


class EnhancedSubFamilySerializer(serializers.ModelSerializer):
    """SubFamily serializer with nested family data"""
    family = FamilySerializer(read_only=True)

    class Meta:
        model = SubFamily
        fields = ['id', 'name', 'code', 'family']


class EnhancedArticleSerializer(serializers.ModelSerializer):
    """Article serializer with nested subfamily and family data"""
    sub_family = EnhancedSubFamilySerializer(read_only=True)

    class Meta:
        model = Article
        fields = ['id', 'name', 'code', 'price', 'sub_family']


class InventoryItemSerializer(serializers.ModelSerializer):
    article = EnhancedArticleSerializer(read_only=True)
    article_id = serializers.UUIDField(write_only=True)
    is_low_stock = serializers.ReadOnlyField()
    stock_value = serializers.ReadOnlyField()
    current_stock = serializers.ReadOnlyField()
    expired_stock = serializers.ReadOnlyField()
    expiring_soon_stock = serializers.ReadOnlyField()
    batches = InventoryBatchSerializer(many=True, read_only=True)

    class Meta:
        model = InventoryItem
        fields = [
            'id', 'article', 'article_id', 'current_stock', 'minimum_stock',
            'maximum_stock', 'reorder_point', 'average_unit_cost', 'last_restocked',
            'is_active', 'track_expiry', 'default_shelf_life_days',
            'is_low_stock', 'stock_value', 'expired_stock', 'expiring_soon_stock',
            'batches', 'created_at', 'updated_at'
        ]


class StockMovementSerializer(serializers.ModelSerializer):
    inventory_item = InventoryItemSerializer(read_only=True)
    inventory_item_id = serializers.UUIDField(write_only=True)
    batch = InventoryBatchSerializer(read_only=True)
    batch_id = serializers.UUIDField(write_only=True, required=False)
    created_by = UserSerializer(read_only=True)
    total_value = serializers.ReadOnlyField()

    class Meta:
        model = StockMovement
        fields = [
            'id', 'inventory_item', 'inventory_item_id', 'batch', 'batch_id',
            'movement_type', 'quantity', 'unit_cost', 'reference_number',
            'notes', 'order_reference', 'created_by', 'total_value', 'created_at'
        ]

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class SupplierSerializer(serializers.ModelSerializer):
    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'phone', 'email',
            'address', 'is_active', 'created_at', 'updated_at'
        ]


class PurchaseOrderItemSerializer(serializers.ModelSerializer):
    article = ArticleSerializer(read_only=True)
    article_id = serializers.UUIDField(write_only=True)
    total_cost = serializers.ReadOnlyField()
    is_fully_received = serializers.ReadOnlyField()

    class Meta:
        model = PurchaseOrderItem
        fields = [
            'id', 'article', 'article_id', 'quantity_ordered',
            'quantity_received', 'unit_cost', 'total_cost', 'is_fully_received'
        ]


class PurchaseOrderSerializer(serializers.ModelSerializer):
    supplier = SupplierSerializer(read_only=True)
    supplier_id = serializers.UUIDField(write_only=True)
    created_by = UserSerializer(read_only=True)
    items = PurchaseOrderItemSerializer(many=True, read_only=True)

    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'order_number', 'supplier', 'supplier_id', 'status',
            'order_date', 'expected_delivery', 'total_amount', 'notes',
            'created_by', 'items', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        # Auto-generate order number
        import uuid
        validated_data['order_number'] = f"PO-{uuid.uuid4().hex[:8].upper()}"
        return super().create(validated_data)


class InventoryDashboardSerializer(serializers.Serializer):
    """Serializer for inventory dashboard data"""
    total_items = serializers.IntegerField()
    low_stock_items = serializers.IntegerField()
    out_of_stock_items = serializers.IntegerField()
    total_stock_value = serializers.DecimalField(
        max_digits=15, decimal_places=2)
    pending_orders = serializers.IntegerField()
    recent_movements = StockMovementSerializer(many=True)
    low_stock_alerts = InventoryItemSerializer(many=True)


class StockAdjustmentSerializer(serializers.Serializer):
    """Serializer for stock adjustments"""
    inventory_item_id = serializers.UUIDField()
    adjustment_type = serializers.ChoiceField(choices=[
        ('increase', 'Increase'),
        ('decrease', 'Decrease'),
        ('set', 'Set to specific amount')
    ])
    quantity = serializers.DecimalField(max_digits=10, decimal_places=2)
    reason = serializers.CharField(max_length=255)
    unit_cost = serializers.DecimalField(
        max_digits=10, decimal_places=2, required=False)


class BulkStockUpdateSerializer(serializers.Serializer):
    """Serializer for bulk stock updates"""
    updates = serializers.ListField(
        child=StockAdjustmentSerializer(),
        min_length=1
    )
