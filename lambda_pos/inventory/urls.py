from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    InventoryItemViewSet,
    StockMovementViewSet,
    SupplierViewSet,
    PurchaseOrderViewSet,
    PurchaseOrderItemViewSet
)

router = DefaultRouter()
router.register(r'items', InventoryItemViewSet)
router.register(r'movements', StockMovementViewSet)
router.register(r'suppliers', SupplierViewSet)
router.register(r'purchase-orders', PurchaseOrderViewSet)
router.register(r'purchase-order-items', PurchaseOrderItemViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
