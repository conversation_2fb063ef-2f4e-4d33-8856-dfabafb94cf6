from django.contrib import admin
from .models import InventoryItem, StockMovement, Supplier, PurchaseOrder, PurchaseOrderItem


@admin.register(InventoryItem)
class InventoryItemAdmin(admin.ModelAdmin):
    list_display = ['article', 'current_stock', 'minimum_stock',
                    'reorder_point', 'is_low_stock', 'is_active']
    list_filter = ['is_active']
    search_fields = ['article__name']
    readonly_fields = ['is_low_stock', 'stock_value']


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ['inventory_item', 'movement_type',
                    'quantity', 'unit_cost', 'created_by', 'created_at']
    list_filter = ['movement_type', 'created_at']
    search_fields = ['inventory_item__article__name', 'reference_number']
    readonly_fields = ['total_value']


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['name', 'contact_person', 'phone', 'email', 'is_active']
    list_filter = ['is_active']
    search_fields = ['name', 'contact_person', 'email']


class PurchaseOrderItemInline(admin.TabularInline):
    model = PurchaseOrderItem
    extra = 0


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'supplier', 'status',
                    'total_amount', 'order_date', 'created_by']
    list_filter = ['status', 'order_date']
    search_fields = ['order_number', 'supplier__name']
    inlines = [PurchaseOrderItemInline]
    readonly_fields = ['order_number']


@admin.register(PurchaseOrderItem)
class PurchaseOrderItemAdmin(admin.ModelAdmin):
    list_display = ['purchase_order', 'article', 'quantity_ordered',
                    'quantity_received', 'unit_cost', 'is_fully_received']
    list_filter = []
    search_fields = ['purchase_order__order_number', 'article__name']
