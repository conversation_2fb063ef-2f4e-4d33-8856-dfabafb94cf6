import django_filters
from django_filters import rest_framework as filters
from django.db.models import Q
from .models import InventoryItem


class InventoryItemFilter(filters.FilterSet):
    name = django_filters.CharFilter(
        field_name='article__name', lookup_expr='icontains')
    code = django_filters.CharFilter(
        field_name='article__code', lookup_expr='icontains')
    family = django_filters.CharFilter(
        field_name='article__sub_family__family__name', lookup_expr='icontains')
    sub_family = django_filters.CharFilter(
        field_name='article__sub_family__name', lookup_expr='icontains')
    is_active = django_filters.BooleanFilter(
        field_name='is_active', lookup_expr='exact')
    search = django_filters.CharFilter(
        method='filter_search', label='Search', required=False)

    def filter_search(self, queryset, name, value):
        if value:
            return queryset.filter(
                Q(article__name__icontains=value) |
                Q(article__code__icontains=value) |
                Q(article__description__icontains=value)
            )
        return queryset

    class Meta:
        model = InventoryItem
        fields = ['name', 'code', 'family',
                  'sub_family', 'is_active', 'search']
