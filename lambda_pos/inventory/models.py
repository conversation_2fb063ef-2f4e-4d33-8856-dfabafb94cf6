from decimal import Decimal
from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from datetime import timedelta
from accounts.models import User
from core.models import BaseModel
from products.models import Article


class InventoryItem(BaseModel):
    """Track inventory levels for articles"""
    article = models.OneToOneField(
        Article, on_delete=models.CASCADE, related_name='inventory')
    minimum_stock = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))]
    )
    maximum_stock = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))]
    )
    reorder_point = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))]
    )
    average_unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))]
    )
    last_restocked = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    # Tracking settings
    track_expiry = models.BooleanField(default=False)
    default_shelf_life_days = models.PositiveIntegerField(
        null=True, blank=True)

    @property
    def current_stock(self):
        """Calculate current stock from all batches"""
        return sum(batch.current_quantity for batch in self.batches.filter(current_quantity__gt=0))

    @property
    def is_low_stock(self):
        return self.current_stock <= self.reorder_point

    @property
    def stock_value(self):
        """Calculate total stock value from all batches"""
        return sum(batch.current_quantity * batch.unit_cost for batch in self.batches.filter(current_quantity__gt=0))

    @property
    def expired_stock(self):
        """Get quantity of expired stock"""
        if not self.track_expiry:
            return 0
        today = timezone.now().date()
        return sum(batch.current_quantity for batch in self.batches.filter(
            expiry_date__lt=today,
            current_quantity__gt=0
        ))

    @property
    def expiring_soon_stock(self):
        """Get quantity of stock expiring in next 7 days"""
        if not self.track_expiry:
            return 0
        today = timezone.now().date()
        week_from_now = today + timedelta(days=7)
        return sum(batch.current_quantity for batch in self.batches.filter(
            expiry_date__gte=today,
            expiry_date__lte=week_from_now,
            current_quantity__gt=0
        ))

    def get_fifo_batches(self):
        """Get batches ordered by FIFO (oldest first)"""
        return self.batches.filter(current_quantity__gt=0).order_by('expiry_date', 'created_at')

    def deduct_stock_fifo(self, quantity_to_deduct):
        """Deduct stock using FIFO principle"""
        remaining_to_deduct = Decimal(str(quantity_to_deduct))
        deductions = []

        for batch in self.get_fifo_batches():
            if remaining_to_deduct <= 0:
                break

            deduction_from_batch = min(
                batch.current_quantity, remaining_to_deduct)
            batch.current_quantity -= deduction_from_batch
            batch.save()

            deductions.append({
                'batch': batch,
                'quantity': deduction_from_batch
            })

            remaining_to_deduct -= deduction_from_batch

        return deductions, remaining_to_deduct

    def __str__(self):
        return f"{self.article.name} - Stock: {self.current_stock}"


class InventoryBatch(BaseModel):
    """Track individual batches of inventory with expiry dates"""
    inventory_item = models.ForeignKey(
        InventoryItem, on_delete=models.CASCADE, related_name='batches')
    batch_number = models.CharField(max_length=100, null=True, blank=True)

    # Quantities
    initial_quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))]
    )
    current_quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))]
    )

    # Cost and dates
    unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))]
    )
    received_date = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateField(null=True, blank=True)

    # References
    purchase_order_reference = models.CharField(
        max_length=100, null=True, blank=True)
    supplier_reference = models.CharField(
        max_length=100, null=True, blank=True)

    @property
    def is_expired(self):
        if not self.expiry_date:
            return False
        return self.expiry_date < timezone.now().date()

    @property
    def days_until_expiry(self):
        if not self.expiry_date:
            return None
        delta = self.expiry_date - timezone.now().date()
        return delta.days

    @property
    def is_expiring_soon(self):
        """Check if batch expires within 7 days"""
        days = self.days_until_expiry
        return days is not None and 0 <= days <= 7

    @property
    def total_value(self):
        return self.current_quantity * self.unit_cost

    def __str__(self):
        batch_info = f"Batch {self.batch_number}" if self.batch_number else f"Batch #{self.id}"
        return f"{self.inventory_item.article.name} - {batch_info} - Qty: {self.current_quantity}"


class StockMovement(BaseModel):
    """Track all stock movements (in/out)"""
    class MOVEMENT_TYPE:
        PURCHASE = "Purchase"
        SALE = "Sale"
        ADJUSTMENT = "Adjustment"
        WASTE = "Waste"
        TRANSFER = "Transfer"
        EXPIRY = "Expiry"

        CHOICES = [
            (PURCHASE, "Purchase"),
            (SALE, "Sale"),
            (ADJUSTMENT, "Adjustment"),
            (WASTE, "Waste"),
            (TRANSFER, "Transfer"),
            (EXPIRY, "Expiry"),
        ]

    inventory_item = models.ForeignKey(
        InventoryItem, on_delete=models.CASCADE, related_name='movements')
    batch = models.ForeignKey(
        'InventoryBatch', on_delete=models.CASCADE, related_name='movements', null=True, blank=True)
    movement_type = models.CharField(
        max_length=20, choices=MOVEMENT_TYPE.CHOICES)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    reference_number = models.CharField(max_length=100, null=True, blank=True)
    notes = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    # Order reference for sales movements
    order_reference = models.CharField(max_length=100, null=True, blank=True)

    @property
    def total_value(self):
        return abs(self.quantity) * self.unit_cost

    def __str__(self):
        batch_info = f" (Batch: {self.batch.batch_number})" if self.batch and self.batch.batch_number else ""
        return f"{self.inventory_item.article.name} - {self.movement_type}: {self.quantity}{batch_info}"


class Supplier(BaseModel):
    """Supplier information for inventory purchases"""
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=255, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    address = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class PurchaseOrder(BaseModel):
    """Purchase orders for inventory restocking"""
    class STATUS:
        DRAFT = "Draft"
        PENDING = "Pending"
        APPROVED = "Approved"
        RECEIVED = "Received"
        CANCELLED = "Cancelled"

        CHOICES = [
            (DRAFT, "Draft"),
            (PENDING, "Pending"),
            (APPROVED, "Approved"),
            (RECEIVED, "Received"),
            (CANCELLED, "Cancelled"),
        ]

    order_number = models.CharField(max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.CASCADE)
    status = models.CharField(
        max_length=20, choices=STATUS.CHOICES, default=STATUS.DRAFT)
    order_date = models.DateTimeField(auto_now_add=True)
    expected_delivery = models.DateTimeField(null=True, blank=True)
    total_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0)
    notes = models.TextField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)

    def __str__(self):
        return f"PO-{self.order_number} - {self.supplier.name}"


class PurchaseOrderItem(BaseModel):
    """Items in a purchase order"""
    purchase_order = models.ForeignKey(
        PurchaseOrder, on_delete=models.CASCADE, related_name='items')
    article = models.ForeignKey(Article, on_delete=models.CASCADE)
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_received = models.DecimalField(
        max_digits=10, decimal_places=2, default=0)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=2)

    @property
    def total_cost(self):
        return self.quantity_ordered * self.unit_cost

    @property
    def is_fully_received(self):
        return self.quantity_received >= self.quantity_ordered

    def __str__(self):
        return f"{self.article.name} - Ordered: {self.quantity_ordered}"
