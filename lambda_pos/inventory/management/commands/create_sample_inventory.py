from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from decimal import Decimal
from datetime import date, timedelta
from products.models import Article
from inventory.models import InventoryItem, InventoryBatch, StockMovement
from inventory.services import InventoryService

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample inventory data for testing'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample inventory data...')

        # Get or create a user for stock movements
        user = User.objects.filter(is_superuser=True).first()
        if not user:
            self.stdout.write(self.style.ERROR(
                'No superuser found. Please create one first.'))
            return

        # Get some articles to create inventory for
        articles = Article.objects.all()[:10]  # Get first 10 articles

        if not articles:
            self.stdout.write(self.style.ERROR(
                'No articles found. Please create some articles first.'))
            return

        created_count = 0

        for i, article in enumerate(articles):
            # Check if inventory item already exists
            inventory_item, created = InventoryItem.objects.get_or_create(
                article=article,
                defaults={
                    'minimum_stock': Decimal('10'),
                    'maximum_stock': Decimal('100'),
                    'reorder_point': Decimal('20'),
                    'average_unit_cost': Decimal('5.00'),
                    'is_active': True,
                    'track_expiry': i % 3 == 0,  # Every 3rd item tracks expiry
                    'default_shelf_life_days': 30 if i % 3 == 0 else None
                }
            )

            if created:
                created_count += 1

                # Create some initial stock batches
                # Varying stock levels
                initial_stock = Decimal('50') + Decimal(str(i * 5))
                unit_cost = Decimal('5.00') + Decimal(str(i * 0.50))

                # Create initial batch
                expiry_date = None
                if inventory_item.track_expiry:
                    expiry_date = date.today() + timedelta(days=30)

                batch = InventoryService.add_stock_batch(
                    inventory_item=inventory_item,
                    quantity=initial_stock,
                    unit_cost=unit_cost,
                    expiry_date=expiry_date,
                    batch_number=f"INIT-{article.id}-001",
                    purchase_order_ref="PO-INITIAL-001",
                    supplier_ref="Initial Stock",
                    user=user
                )

                # Create some stock movements for variety
                if i % 2 == 0:  # Every other item gets some sales
                    # Simulate some sales
                    sale_quantity = Decimal('5') + Decimal(str(i * 2))
                    deductions, remaining = inventory_item.deduct_stock_fifo(
                        sale_quantity)

                    for deduction in deductions:
                        StockMovement.objects.create(
                            inventory_item=inventory_item,
                            batch=deduction['batch'],
                            movement_type=StockMovement.MOVEMENT_TYPE.SALE,
                            quantity=-deduction['quantity'],
                            unit_cost=deduction['batch'].unit_cost,
                            order_reference=f"ORDER-{i+1:03d}",
                            notes=f"Sample sale for {article.name}",
                            created_by=user
                        )

                if i % 4 == 0:  # Every 4th item gets an adjustment
                    # Simulate stock adjustment
                    InventoryService.adjust_stock(
                        inventory_item=inventory_item,
                        adjustment_type='increase',
                        quantity=Decimal('10'),
                        reason=f"Stock count adjustment for {article.name}",
                        unit_cost=unit_cost,
                        user=user
                    )

                self.stdout.write(f'Created inventory for: {article.name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_count} inventory items with sample data'
            )
        )
