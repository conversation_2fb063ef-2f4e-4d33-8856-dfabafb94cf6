# Generated by Django 5.2.3 on 2025-07-28 19:32

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='inventoryitem',
            old_name='unit_cost',
            new_name='average_unit_cost',
        ),
        migrations.RemoveField(
            model_name='inventoryitem',
            name='current_stock',
        ),
        migrations.AddField(
            model_name='inventoryitem',
            name='default_shelf_life_days',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='inventoryitem',
            name='track_expiry',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='order_reference',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='stockmovement',
            name='movement_type',
            field=models.CharField(choices=[('Purchase', 'Purchase'), ('Sale', 'Sale'), ('Adjustment', 'Adjustment'), ('Waste', 'Waste'), ('Transfer', 'Transfer'), ('Expiry', 'Expiry')], max_length=20),
        ),
        migrations.CreateModel(
            name='InventoryBatch',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('batch_number', models.CharField(blank=True, max_length=100, null=True)),
                ('initial_quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))])),
                ('current_quantity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))])),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0'))])),
                ('received_date', models.DateTimeField(auto_now_add=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('purchase_order_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('supplier_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='inventory.inventoryitem')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='stockmovement',
            name='batch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.inventorybatch'),
        ),
    ]
