from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from orders.models import OrderItem
from .services import InventoryService
import logging

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(post_save, sender=OrderItem)
def handle_order_item_stock_deduction(sender, instance, created, **kwargs):
    """
    Automatically deduct stock when an OrderItem is created
    Only if stock tracking is enabled in AppSettings
    """
    if not created:
        return  # Only handle new order items
    
    if not InventoryService.is_stock_tracking_enabled():
        return  # Stock tracking disabled
    
    try:
        # Get the user who created the order (fallback to first superuser if not available)
        user = None
        if hasattr(instance.order, 'created_by'):
            user = instance.order.created_by
        else:
            # Fallback to first superuser
            user = User.objects.filter(is_superuser=True).first()
        
        # Attempt to deduct stock
        success = InventoryService.deduct_stock_for_order_item(
            article=instance.article,
            quantity=instance.quantity,
            order_reference=instance.order.order_number,
            user=user
        )
        
        if not success:
            logger.warning(
                f"Insufficient stock for OrderItem {instance.id}: "
                f"Article {instance.article.name}, Quantity {instance.quantity}, "
                f"Order {instance.order.order_number}"
            )
            # Note: We don't prevent the order creation, just log the warning
            # The business logic should handle insufficient stock scenarios
        else:
            logger.info(
                f"Stock deducted for OrderItem {instance.id}: "
                f"Article {instance.article.name}, Quantity {instance.quantity}, "
                f"Order {instance.order.order_number}"
            )
            
    except Exception as e:
        logger.error(
            f"Error deducting stock for OrderItem {instance.id}: {str(e)}"
        )


@receiver(post_delete, sender=OrderItem)
def handle_order_item_stock_restoration(sender, instance, **kwargs):
    """
    Restore stock when an OrderItem is deleted (order cancellation)
    Only if stock tracking is enabled
    """
    if not InventoryService.is_stock_tracking_enabled():
        return
    
    try:
        # Get user for the restoration
        user = None
        if hasattr(instance.order, 'created_by'):
            user = instance.order.created_by
        else:
            user = User.objects.filter(is_superuser=True).first()
        
        # Add stock back using adjustment
        from .models import InventoryItem
        try:
            inventory_item = InventoryItem.objects.get(
                article=instance.article, 
                is_active=True
            )
            
            InventoryService.adjust_stock(
                inventory_item=inventory_item,
                adjustment_type='increase',
                quantity=instance.quantity,
                reason=f"Order cancellation - Order {instance.order.order_number}",
                unit_cost=inventory_item.average_unit_cost,
                user=user
            )
            
            logger.info(
                f"Stock restored for cancelled OrderItem: "
                f"Article {instance.article.name}, Quantity {instance.quantity}, "
                f"Order {instance.order.order_number}"
            )
            
        except InventoryItem.DoesNotExist:
            logger.warning(
                f"No inventory item found for article {instance.article.name} "
                f"when trying to restore stock for cancelled order"
            )
            
    except Exception as e:
        logger.error(
            f"Error restoring stock for cancelled OrderItem {instance.id}: {str(e)}"
        )
