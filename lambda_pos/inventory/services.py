from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from datetime import timedel<PERSON>
from .models import InventoryItem, InventoryBatch, StockMovement
from general.models import AppSetting


class InventoryService:
    """Service for handling inventory operations with FIFO logic"""

    @staticmethod
    def is_stock_tracking_enabled():
        """Check if stock tracking is enabled in app settings"""
        try:
            setting = AppSetting.objects.first()
            return setting.track_stock if setting else False
        except AppSetting.DoesNotExist:
            return False

    @staticmethod
    def deduct_stock_for_order_item(article, quantity, order_reference=None, user=None):
        """
        Deduct stock for an order item using FIFO principle
        Returns True if successful, False if insufficient stock
        """
        if not InventoryService.is_stock_tracking_enabled():
            return True  # Stock tracking disabled, allow order

        try:
            inventory_item = InventoryItem.objects.get(
                article=article, is_active=True)
        except InventoryItem.DoesNotExist:
            # No inventory item exists, create one with zero stock
            inventory_item = InventoryItem.objects.create(
                article=article,
                minimum_stock=0,
                reorder_point=0,
                average_unit_cost=0,
                is_active=True
            )

        # Check if we have enough stock
        current_stock = inventory_item.current_stock
        if current_stock < quantity:
            return False  # Insufficient stock

        # Deduct stock using FIFO
        with transaction.atomic():
            deductions, remaining = inventory_item.deduct_stock_fifo(quantity)

            # Create stock movement records for each batch deduction
            for deduction in deductions:
                StockMovement.objects.create(
                    inventory_item=inventory_item,
                    batch=deduction['batch'],
                    movement_type=StockMovement.MOVEMENT_TYPE.SALE,
                    quantity=-deduction['quantity'],  # Negative for outgoing
                    unit_cost=deduction['batch'].unit_cost,
                    order_reference=order_reference,
                    notes=f"Sale deduction for order {order_reference}",
                    created_by=user
                )

        return True

    @staticmethod
    def add_stock_batch(inventory_item, quantity, unit_cost, expiry_date=None,
                        batch_number=None, purchase_order_ref=None, supplier_ref=None, user=None):
        """Add a new batch of stock to inventory"""
        with transaction.atomic():
            # Calculate expiry date if not provided but tracking is enabled
            if inventory_item.track_expiry and not expiry_date and inventory_item.default_shelf_life_days:
                expiry_date = timezone.now().date() + \
                    timedelta(days=inventory_item.default_shelf_life_days)

            # Create new batch
            batch = InventoryBatch.objects.create(
                inventory_item=inventory_item,
                batch_number=batch_number,
                initial_quantity=quantity,
                current_quantity=quantity,
                unit_cost=unit_cost,
                expiry_date=expiry_date,
                purchase_order_reference=purchase_order_ref,
                supplier_reference=supplier_ref
            )

            # Update average unit cost using weighted average
            total_value = inventory_item.stock_value + (quantity * unit_cost)
            total_quantity = inventory_item.current_stock + quantity

            if total_quantity > 0:
                inventory_item.average_unit_cost = total_value / total_quantity

            inventory_item.last_restocked = timezone.now()
            inventory_item.save()

            # Create stock movement record
            StockMovement.objects.create(
                inventory_item=inventory_item,
                batch=batch,
                movement_type=StockMovement.MOVEMENT_TYPE.PURCHASE,
                quantity=quantity,
                unit_cost=unit_cost,
                reference_number=purchase_order_ref,
                notes=f"Stock received - Batch {batch_number or batch.id}",
                created_by=user
            )

            return batch

    @staticmethod
    def adjust_stock(inventory_item, adjustment_type, quantity, reason, unit_cost=None, user=None):
        """Adjust stock levels manually"""
        with transaction.atomic():
            if adjustment_type == 'increase':
                # Add new batch for increase
                batch = InventoryService.add_stock_batch(
                    inventory_item=inventory_item,
                    quantity=quantity,
                    unit_cost=unit_cost or inventory_item.average_unit_cost,
                    batch_number=f"ADJ-{timezone.now().strftime('%Y%m%d-%H%M%S')}",
                    user=user
                )

                StockMovement.objects.create(
                    inventory_item=inventory_item,
                    batch=batch,
                    movement_type=StockMovement.MOVEMENT_TYPE.ADJUSTMENT,
                    quantity=quantity,
                    unit_cost=unit_cost or inventory_item.average_unit_cost,
                    notes=reason,
                    created_by=user
                )

            elif adjustment_type == 'decrease':
                # Deduct using FIFO
                deductions, remaining = inventory_item.deduct_stock_fifo(
                    quantity)

                for deduction in deductions:
                    StockMovement.objects.create(
                        inventory_item=inventory_item,
                        batch=deduction['batch'],
                        movement_type=StockMovement.MOVEMENT_TYPE.ADJUSTMENT,
                        quantity=-deduction['quantity'],
                        unit_cost=deduction['batch'].unit_cost,
                        notes=reason,
                        created_by=user
                    )

            elif adjustment_type == 'set':
                # Set to specific amount
                current_stock = inventory_item.current_stock
                difference = quantity - current_stock

                if difference > 0:
                    # Need to add stock
                    InventoryService.adjust_stock(
                        inventory_item, 'increase', difference, reason, unit_cost, user
                    )
                elif difference < 0:
                    # Need to remove stock
                    InventoryService.adjust_stock(
                        inventory_item, 'decrease', abs(
                            difference), reason, unit_cost, user
                    )

    @staticmethod
    def mark_expired_stock(inventory_item, user=None):
        """Mark expired stock as waste"""
        today = timezone.now().date()
        expired_batches = inventory_item.batches.filter(
            expiry_date__lt=today,
            current_quantity__gt=0
        )

        with transaction.atomic():
            for batch in expired_batches:
                if batch.current_quantity > 0:
                    # Create waste movement
                    StockMovement.objects.create(
                        inventory_item=inventory_item,
                        batch=batch,
                        movement_type=StockMovement.MOVEMENT_TYPE.EXPIRY,
                        quantity=-batch.current_quantity,
                        unit_cost=batch.unit_cost,
                        notes=f"Expired stock - Expiry date: {batch.expiry_date}",
                        created_by=user
                    )

                    # Set batch quantity to zero
                    batch.current_quantity = 0
                    batch.save()

    @staticmethod
    def get_stock_alerts():
        """Get stock alerts for low stock and expiring items"""
        alerts = {
            'low_stock': [],
            'out_of_stock': [],
            'expiring_soon': [],
            'expired': []
        }

        for item in InventoryItem.objects.filter(is_active=True):
            current_stock = item.current_stock

            # Low stock check
            if current_stock <= item.reorder_point and current_stock > 0:
                alerts['low_stock'].append(item)

            # Out of stock check
            if current_stock == 0:
                alerts['out_of_stock'].append(item)

            # Expiry checks
            if item.track_expiry:
                if item.expiring_soon_stock > 0:
                    alerts['expiring_soon'].append(item)
                if item.expired_stock > 0:
                    alerts['expired'].append(item)

        return alerts
