from accounts.models import User
from colorfield.fields import ColorField
from core.models import BaseModel
from core.utils.renders import get_dynamic_color_palette
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from literals.models import ProductType
from literals.models import UnitMeasure
from literals.models import Workstation


class ColorMixin:
    @property
    def generated_color(self):
        if self.color:
            return self.color
        return get_dynamic_color_palette(self.id)


class Family(ColorMixin, BaseModel):
    name = models.CharField(max_length=255, blank=True, null=True)
    disabled = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    code = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    button_label = models.CharField(max_length=100, blank=True, null=True)
    button_label_hide = models.BooleanField(default=False)
    button_image = models.ImageField(upload_to="family_buttons/", blank=True, null=True)
    button_icon = models.CharField(max_length=100, blank=True, null=True)
    commission_group = models.CharField(max_length=100, blank=True, null=True)
    discount_group = models.CharField(max_length=100, blank=True, null=True)
    # printer = models.ForeignKey(Printer, on_delete=models.CASCADE,null=True,blank=True)
    template = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    color = ColorField(default="#0066cc", null=True, blank=True)

    def __str__(self):
        return f"{self.code} - {self.description}"


@receiver(post_save, sender=Family)
def set_default_color(sender, instance: Family, created, **kwargs):
    if created and not instance.color:
        instance.color = get_dynamic_color_palette(instance.id)
        instance.save(update_fields=["color"])


class SubFamily(ColorMixin, BaseModel):
    name = models.CharField(max_length=255, blank=True, null=True)
    family = models.ForeignKey(Family, on_delete=models.CASCADE)
    disabled = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    code = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    button_label = models.CharField(max_length=100, blank=True, null=True)
    button_label_hide = models.BooleanField(default=False)
    button_image = models.ImageField(
        upload_to="article_family_buttons/", blank=True, null=True
    )
    button_icon = models.CharField(max_length=100, blank=True, null=True)
    commission_group = models.CharField(max_length=100, blank=True, null=True)
    discount_group = models.CharField(max_length=100, blank=True, null=True)
    template = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    color = ColorField(default="#0066cc", null=True, blank=True)

    def __str__(self):
        return f"{self.code} - {self.description or ''}"


@receiver(post_save, sender=SubFamily)
def set_default_color_subfamily(sender, instance: SubFamily, created, **kwargs):
    if created and not instance.color:
        instance.color = get_dynamic_color_palette(instance.id)
        instance.save(update_fields=["color"])


class Article(ColorMixin, BaseModel):
    name = models.CharField(max_length=255, blank=True, null=True)
    sub_family = models.ForeignKey(SubFamily, on_delete=models.CASCADE)
    product_type = models.ForeignKey(
        ProductType, on_delete=models.CASCADE, null=True, blank=True
    )
    unit_measure = models.ForeignKey(
        UnitMeasure, on_delete=models.CASCADE, null=True, blank=True
    )
    is_favorite = models.BooleanField(default=False)
    quantity = models.PositiveSmallIntegerField(default=0)
    price = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True, default=0
    )
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    disabled = models.BooleanField(default=False)
    notes = models.TextField(blank=True, null=True)
    code = models.CharField(max_length=50, unique=True)
    code_dealer = models.CharField(max_length=50, blank=True, null=True)
    description = models.CharField(max_length=255, blank=True, null=True)
    button_label = models.CharField(max_length=100, blank=True, null=True)
    button_label_hide = models.BooleanField(default=False)
    button_image = models.ImageField(
        upload_to="article_buttons/", blank=True, null=True
    )
    button_icon = models.CharField(max_length=100, blank=True, null=True)

    price_with_vat = models.BooleanField(default=False)
    discount = models.DecimalField(
        max_digits=5, decimal_places=2, blank=True, null=True
    )
    default_quantity = models.PositiveSmallIntegerField(default=1)
    minimum_stock = models.DecimalField(
        max_digits=10, decimal_places=2, blank=True, null=True
    )
    barcode = models.CharField(max_length=100, blank=True, null=True)
    favorite = models.BooleanField(default=False)
    template = models.TextField(blank=True, null=True)
    template_barcode = models.TextField(blank=True, null=True)
    is_unique = models.BooleanField(default=False)
    workstations = models.ForeignKey(
        Workstation, on_delete=models.CASCADE, null=True, blank=True
    )
    color = ColorField(null=True, blank=True)

    def __str__(self):
        return f"{self.code} - {self.description or ''}"

    @property
    def generated_color(self):
        return


@receiver(post_save, sender=Article)
def set_default_workstation(sender, instance: Article, created, **kwargs):
    if not instance.workstations:
        active_ws = Workstation.objects.filter(is_active=True).first()
        if not active_ws:
            return
        instance.workstations = active_ws
        instance.save(update_fields=["workstations"])
